# URLS
domain: app.digisac.io
hostWebchat: webchat.digisac.app
sentryDsn: null
sentryDsnFront: null

# GENERAL
namespace: app
registryCredentialName: ocirsecret

nodeAffinity:
  key: pool
  value:
    - statefull

statelessNodeAffinity:
  key: pool
  value:
    - stateless-spot
    - stateless-fallback

# IMAGES
imageBackuper: "sa-vinhedo-1.ocir.io/axvaplbwrlcl/digisac/backuper:latest"
imageFront: "sa-vinhedo-1.ocir.io/axvaplbwrlcl/digisac-isolated/front:3.47.0"
imageCron: "sa-vinhedo-1.ocir.io/axvaplbwrlcl/digisac-isolated/back:3.48.2-mr-2376.80"
imageSocket: "sa-vinhedo-1.ocir.io/axvaplbwrlcl/digisac-isolated/back:3.48.2-mr-2376.80"
imageApi: "sa-vinhedo-1.ocir.io/axvaplbwrlcl/digisac-isolated/back:3.48.2-mr-2376.80"
imageWorkers: "sa-vinhedo-1.ocir.io/axvaplbwrlcl/digisac-isolated/back:3.48.2-mr-2376.80"
imageWorkersGo: "sa-vinhedo-1.ocir.io/axvaplbwrlcl/digisac-go:1.17.0"
imageMessageBrokerProducer: "sa-vinhedo-1.ocir.io/axvaplbwrlcl/message-broker/producer:1.3.0"
imageMessageBrokerConsumer: "sa-vinhedo-1.ocir.io/axvaplbwrlcl/message-broker/consumer:1.3.0"
imageDbMigration: "sa-vinhedo-1.ocir.io/axvaplbwrlcl/digisac-isolated/back:3.48.2-mr-2376.80"
imageBrowserless: "browserless/chrome:1.60-chrome-stable"


# AUTOSCALER
autoscaler:
  api:
    min: 1
    max: 6
  front:
    min: 1
    max: 3
  podgateway:
    min: 1 # Com mais de 1 replica, não funcionou conexão
    max: 1
  socket:
    min: 1
    max: 3
  workers:
    min: 1
    max: 3
  workersGo:
    min: 1
    max: 3
  messageBrokerProducer:
    min: 1
    max: 2
  messageBrokerConsumer:
    min: 1
    max: 3
  browserless:
    min: 1
    max: 1

# RESOURCE LIMITS
resourceLimits:
  api:
    reqCpu: 200m
    reqMem: 256M
    limCpu: 1
    limMem: 512M
  browserless:
    reqCpu: 200m
    reqMem: 128M
    limCpu: 500m
    limMem: 512M
  cron:
    reqCpu: 100m
    reqMem: 128M
    limCpu: 100m
    limMem: 192M
  front:
    reqCpu: 200m
    reqMem: 64M
    limCpu: 300m
    limMem: 256M
  podgateway:
    reqCpu: 100m
    reqMem: 128M
    limCpu: 500m
    limMem: 512M
  socket:
    reqCpu: 300m
    reqMem: 128M
    limCpu: 500m
    limMem: 512M
  workers:
    reqCpu: 500m
    reqMem: 512M
    limCpu: 1
    limMem: 1G
    nodeMem: null # Value in Mi (ex. 2048)
  workersGo:
    reqCpu: 200m
    reqMem: 256M
    limCpu: 500m
    limMem: 512M
  messageBrokerProducer:
    reqCpu: 100m
    reqMem: 128M
    limCpu: 200m
    limMem: 256M
  messageBrokerConsumer:
    reqCpu: 200m
    reqMem: 256M
    limCpu: 500m
    limMem: 512M



# CONFIGURATIONS
configEnvs:
  nodeEnv: production
  deployment: digisac-production
  timeZone: utc
  debug: app:*
  logFormat: text
  storageDriver: s3
  awsAccessKeyId: ********************
  awsSecretAccessKey: dG6zufLuURDCDaGA2E7IZ3pW3HG8UZgHZGyKE3Fv
  awsBucketName: mandeumzap-storage
  awsS3Endpoint: s3.us-east-1.amazonaws.com
  awsRegion: us-east-1
  awsExportsBucketName: null
  oracleAccessKeyId: null
  oracleSecretAccessKey: null
  oracleBucketName: null
  oracleRegion: null
  oracleEndpoint: null
  oracleEndpointReader: null
  wplvS3AccessKeyId: ********************
  wplvS3SecretAccessKey: wbnSb2c/QHCbPyTYWhdT9/FVzriXM7seo4ePlKxr
  wplvS3Endpoint: s3.us-east-1.amazonaws.com
  wplvS3BucketName: oke-digisac-wplv
  wplvS3Region: us-east-1
  wpBrowserDataS3AccessKeyId: ********************
  wpBrowserDataS3SecretAccessKey: 1g9GI4hUEl8IEbU/dQMJJfhyH2nSBdRMjGCPtcX9
  wpBrowserDataS3Endpoint: s3.us-east-1.amazonaws.com
  wpBrowserDataS3BucketName: oke-digisac-browserdata
  wpBrowserDataS3Region: us-east-1
  emailFrom: <EMAIL>
  encryptionKey: cd6fb4179d7e818a2674d0b3d5fbe4cd79ea48a30470d5346486494ececd113c
  domain: digisac.io
  publicUrl: https://app.digisac.io/api/v1
  frontUrl: https://app.digisac.io
  workersUrl: http://app-workers.app.svc.cluster.local
  workersGoUrl: http://app-workers-go.app.svc.cluster.local:8000
  workerGoServices: "telegram,whatsapp-business"
  workersPort: null
  amqpUrl: amqp://default_user_Bwq7ZlYY5r6t2EVnqGx:<EMAIL>
  redisUrl: redis://dragonfly.dragonfly.svc.cluster.local:6379
  dbHost: pgbouncer.pgbouncer.svc.cluster.local
#  dbHost: digisac-cluster-1-postgres.postgresprivsub.digisacvcn.oraclevcn.com
  dbDatabase: mandeumzap
  dbUsername: postgres
  dbPassword: "LvJNqnD5cXxvcCFXu5DWv8KxHkxaFuMP"
  dbPort: "5432"
  dbMaxConnections: "90"
  webchatUrl: https://webchat-api.digisac.app
  driversGatewayUrl: https://gateway.digisac.app
  desiredServerPodAvailableCount: "2"
  apmUrl: https://apm2.ikatec.cloud
  apmSecret: vnK9jGS1A7hBZ35116z5s2PJ
  agnusCloudToken: "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImp0aSI6IjRkYjczYjdmY2Q4MjIyNjE0NmY0NzIwNzg3Yzc3MDEzNWY2MjYwMDZlNGZhNGNmMGIyMzBhNGNiYTdjYmE3NjYxOTNiYmQ5OGJmNmRhNzdiIn0.eyJhdWQiOiIxIiwianRpIjoiNGRiNzNiN2ZjZDgyMjI2MTQ2ZjQ3MjA3ODdjNzcwMTM1ZjYyNjAwNmU0ZmE0Y2YwYjIzMGE0Y2JhN2NiYTc2NjE5M2JiZDk4YmY2ZGE3N2IiLCJpYXQiOjE2Mjc2NzI1NTYsIm5iZiI6MTYyNzY3MjU1NiwiZXhwIjo0MjU3NTE1NzU2LCJzdWIiOiIzMzciLCJzY29wZXMiOltdfQ.ninX0Jd_5YURfyqU9v_GdS1KfA6acm0vIqOqvH0S5bnfYyyr7h-Udyeuf_mFY00t9A8_aVfy0R_j94tLVwT4WFhvDyMWR5EIzVtvfbWp3ojyFbIfYBiF871lDGvnE4O5KnBeonBD6NLESW-gEqRou-iuNGOlv5iSu0nCkRNEDWsAqw2G2-6gnQktL7L6mOQvQ1h3e6jM8ociM5aKtKB6b2I_rBC3Cu7yGv1gp3xk7QmiBsuftho9IFf0j1etmo1RFFwtDbYqDOAKFBPwV4B3EN98tx56eCXd9z1XCNS0PvXShzozjAZDbIPtIyCLF5rXOK1QyBvCuUiBsQoZVLx05jeS0LmdSvzTvsrCc9EHU7pStb-ciPXu0xpyBY-4S_4r2lqwo4QkBVpjj4Udvac7Osu_n1YTRbqEDLXxo_aMKY-QCKl75BTtCZfsXZzYl8B_Lv1v0pcRuLK6H4jCiohmPIIsYYurI7bYk-t5Y1-tGK3I8ATpNL3u0KGJ_7M-VD8h2-PBGHAlJOLxvrQmezDL6RSqpugXKt9R9cgL2Rg_7PLbAsEIbvyqInKeyOYd2vnlnN_KanTnSpOMiB02eIvyWKmrOpCqV7bgj44rCrhHhlXE7HtN2eCnVHI9mteIHF25dxWGCDZ5NPQAql_aIdfLvoGtyr0xh8bIl7_rFndLkqI"
  agnusUrl: https://agnus.app/api/v1
  daysToAlertWebhookInactivation: "10"
  daysForWebhookInactivation: "13"
  add9ToMessageIdAfterTimestamp: "2022-02-02T20:34:18.247Z"
  gmailAppClientId: null
  gmailAppClientSecret: null
  gmailAppRedirectUrl: null
  microsoftClientId: null
  microsoftClientSecret: null
  microsoftLoginBaseUrl: null
  microsoftRedirectUri: null
  microsoftBaseUrl: null
  buildFlagIsClient: null
  smsUsername: null
  smsToken: null
  movilleUrl: null
  verifyToken: null
  internalChatJwt: passaro-nao-voa
  internalChatUrl: https://internalchat-dev.digisac.io
  hub360Username: "<EMAIL>"
  hub360Password: "Digi@3000"
  hub360PartnerId: 5urmEBPA
  refreshTicketCountCronExpression: "*/10 * * * * *"
  minutesToAlertWebhookInactivation: "60"
  minutesForWebhookInactiovation: "1440"
  allowIgnoreStrengthPassword: false
  waHeadFull: false
  ntbaFix319: "1"

# WORKERS-GO CONFIGURATIONS
workersGoEnvs:
  goEnv: "production"
  branch: "default"
  deployment: "digisac-cluster-dev"
  publicUrl: "https://app.digisac.io/api/v1"
  whatsappApiHttpAddress: "http://wall-api-1:5060"
  whatsappApiGrpcAddress: "wall-api-1:50063"
  whatsappApiToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjIyMDYxMjQ5NjksInN1YiI6ImE4ZmFlNDIxLWM4NzMtNDc2ZC04MzJkLWQ3NmM5NDFjM2Q5NSJ9.GoZ4xEiOJOwbBycooiKe0op6MTP6AghdJppmP2du48E"
  workersGoPort: "8000"
  dbHost: "pgbouncer.pgbouncer.svc.cluster.local"
  dbUsername: "postgres"
  dbPassword: "LvJNqnD5cXxvcCFXu5DWv8KxHkxaFuMP"
  dbName: "mandeumzap"
  dbPort: "5432"
  dbMaxConns: "500"
  dbLog: "true"
  logType: "json"
  encryptionKey: "cd6fb4179d7e818a2674d0b3d5fbe4cd79ea48a30470d5346486494ececd113c"
  storageDriver: "oracle"
  awsAccessKeyId: "********************"
  awsSecretAccessKey: "7Qj7PYP19evr530vTrJIKeIthtZ8Y4iBeO02FMO/"
  awsBucketName: "mandeumzap-storage"
  awsRegion: "us-east-1"
  oracleAccessKeyId: "a5bd4c24b8ccd539ff14be271c1af8fb5087094d"
  oracleSecretAccessKey: "5lm8P+nj4K29cCUyQwQEX+FQwSs48wtlUVyBOIo9XtE="
  oracleBucketsNames: "digisac-storage-qa-1,digisac-storage-qa-2,digisac-storage-qa-3"
  oracleRegion: "sa-vinhedo-1"
  oracleEndpoint: "https://axvaplbwrlcl.compat.objectstorage.sa-vinhedo-1.oraclecloud.com"
  gupshupEmail: "<EMAIL>"
  gupshupPassword: "ikaDEV23*"
  facebookAppId: "139638411265417"
  defaultHsmLimit: "3000"
  disableWabaWebhookUrlSet: "false"
  driversGatewayUrl: "https://gateway.digisac.app"
  queueManagerDispatcherUrl: "http://message-broker-producer:2000/dispatch"
  mockDriverUrl: "https://mock-drivers.ikatec.cloud"
  useMockDriver: "false"
  whatsappBusinessWebhookProcessReceivedFileQueueConcurrency: null
  useCachedTicketsCount: "false"
  fileCacheTtl: "300000"
  externalBrowser: "true"
  browserWsEndpointPdfGenerator: "ws://app-browserless:3000?headless=true"
  chromeExecutablePath: null
  browserWsEndpoint: null
  updateFetchCountDebounce: null
  proxyUrl: null
  type: null
  internalApiUrl: null
  socketGatewayAddress: null
  oneSignalUrl: null
  oneSignalAppId: null
  sendgridApiKey: null
  nodeTlsRejectUnauthorized: null
  waSocketRestarterInterval: null
  waBruteForceMessageCheckerInterval: null
  waVersion: null
  facebookApiUrl: null
  facebookAppId: null
  facebookClientSecret: null
  telegramProfileUrl: null
  telegramWebhookUrl: null
  positusUsername: null
  positusPassword: null
  mongoDbHost: null
  mongoDbPort: null
  mongoDbDatabase: null
  mongoDbUsername: null
  mongoDbPassword: null
  mongoDbMaxPoolSize: null
  mongoDbAuthSource: null
  gupshupEmail: null
  gupshupPassword: null
  expoAccessToken: null
  serverpodBrowserDataBackupActive: null
  apiUrl: https://app.digisac.io/api/v1
  socketUrl: https://app.digisac.io
  mediaHost: app.digisac.io
  beta: "true"
  clientId: null
  clientSecret: null
  googleApiKey: null
  googleAnalyticsTag: GTM-WC224LN9
  serverpodUrl: null