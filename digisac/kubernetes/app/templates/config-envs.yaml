apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config-envs
  annotations:
    reloader.stakater.com/match: "true"
  namespace: {{ .Values.namespace }}
data:
  NODE_ENV: "{{ .Values.configEnvs.nodeEnv }}"
  DEPLOYMENT: "{{ .Values.configEnvs.deployment }}"
  TZ: "{{ .Values.configEnvs.timeZone }}"
  DEBUG: "{{ .Values.configEnvs.debug }}"
  LOG_FORMAT: "{{ .Values.configEnvs.logFormat }}"
  STORAGE_DRIVER: "{{ .Values.configEnvs.storageDriver }}"
  AWS_ACCESS_KEY_ID: "{{ .Values.configEnvs.awsAccessKeyId }}"
  AWS_SECRET_ACCESS_KEY: "{{ .Values.configEnvs.awsSecretAccessKey }}"
  AWS_BUCKET_NAME: "{{ .Values.configEnvs.awsBucketName }}"
  AWS_REGION: "{{ .Values.configEnvs.awsRegion }}"
  AWS_S3_ENDPOINT: "{{ .Values.configEnvs.awsS3Endpoint }}"
  ORACLE_ACCESS_KEY_ID: "{{ .Values.configEnvs.oracleAccessKeyId }}"
  ORACLE_SECRET_ACCESS_KEY: "{{ .Values.configEnvs.oracleSecretAccessKey }}"
  ORACLE_NAME: "{{ .Values.configEnvs.oracleBucketName }}"
  ORACLE_REGION: "{{ .Values.configEnvs.oracleRegion }}"
  ORACLE_ENDPOINT: "{{ .Values.configEnvs.oracleEndpoint }}"
  ORACLE_ENDPOINT_READER: "{{ .Values.configEnvs.oracleEndpointReader }}"
  WPLV_S3_ENDPOINT: "{{ .Values.configEnvs.wplvS3Endpoint }}"
  WPLV_S3_ACCESS_KEY_ID: "{{ .Values.configEnvs.wplvS3AccessKeyId }}"
  WPLV_S3_SECRET_ACCESS_KEY: "{{ .Values.configEnvs.wplvS3SecretAccessKey }}"
  WPLV_S3_BUCKET_NAME: "{{ .Values.configEnvs.wplvS3BucketName }}"
  WPLV_S3_REGION: "{{ .Values.configEnvs.wplvS3Region }}"
  WP_BROWSER_DATA_S3_ACCESS_KEY_ID: "{{ .Values.configEnvs.wpBrowserDataS3AccessKeyId }}"
  WP_BROWSER_DATA_S3_SECRET_ACCESS_KEY: "{{ .Values.configEnvs.wpBrowserDataS3SecretAccessKey }}"
  WP_BROWSER_DATA_S3_ENDPOINT: "{{ .Values.configEnvs.wpBrowserDataS3Endpoint }}"
  WP_BROWSER_DATA_S3_BUCKET_NAME: "{{ .Values.configEnvs.wpBrowserDataS3BucketName }}"
  WP_BROWSER_DATA_S3_REGION: "{{ .Values.configEnvs.wpBrowserDataS3Region }}"
  EMAIL_FROM: "{{ .Values.configEnvs.emailFrom }}"
  ENCRYPTION_KEY: "{{ .Values.configEnvs.encryptionKey }}"
  DOMAIN: "{{ .Values.configEnvs.domain }}"
  PUBLIC_URL: "{{ .Values.configEnvs.publicUrl }}"
  FRONT_URL: "{{ .Values.configEnvs.frontUrl }}"
  WORKERS_URL: "{{ .Values.configEnvs.workersUrl }}"
  AMQP_URL: "{{ .Values.configEnvs.amqpUrl }}"
  REDIS_URL: "{{ .Values.configEnvs.redisUrl }}"
  SENTRY_DSN: "{{ .Values.sentryDsn }}"
  DB_HOST: "{{ .Values.configEnvs.dbHost }}"
  DB_DATABASE: "{{ .Values.configEnvs.dbDatabase }}"
  DB_USERNAME: "{{ .Values.configEnvs.dbUsername }}"
  DB_PASSWORD: "{{ .Values.configEnvs.dbPassword }}"
  DB_PORT: "{{ .Values.configEnvs.dbPort }}"
  DB_MAX_CONNECTIONS: "{{ .Values.configEnvs.dbMaxConnections }}"
  WEBCHAT_URL: "{{ .Values.configEnvs.webchatUrl }}"
  DRIVERS_GATEWAY_URL: "{{ .Values.configEnvs.driversGatewayUrl }}"
  DESIRED_SERVER_POD_AVAILABLE_COUNT: "{{ .Values.configEnvs.desiredServerPodAvailableCount }}"
  APM_URL: "{{ .Values.configEnvs.apmUrl }}"
  APM_SECRET: "{{ .Values.configEnvs.apmSecret }}"
  AGNUS_CLOUD_TOKEN: "{{ .Values.configEnvs.agnusCloudToken }}"
  AGNUS_URL: "{{ .Values.configEnvs.agnusUrl }}"
  DAYS_TO_ALERT_WEBHOOK_INACTIVATION: "{{ .Values.configEnvs.daysToAlertWebhookInactivation }}"
  DAYS_FOR_WEBHOOK_INACTIVATION: "{{ .Values.configEnvs.daysForWebhookInactivation }}"
  ADD_9_TO_MESSAGE_ID_AFTER_TIMESTAMP: "{{ .Values.configEnvs.add9ToMessageIdAfterTimestamp }}"
  GMAIL_APP_CLIENT_ID: "{{ .Values.configEnvs.gmailAppClientId }}"
  GMAIL_APP_CLIENT_SECRET: "{{ .Values.configEnvs.gmailAppClientSecret }}"
  GMAIL_APP_REDIRECT_URL: "{{ .Values.configEnvs.gmailAppRedirectUrl }}"
  MICROSOFT_CLIENT_ID: "{{ .Values.configEnvs.microsoftClientId }}"
  MICROSOFT_CLIENT_SECRET: "{{ .Values.configEnvs.microsoftClientSecret }}"
  MICROSOFT_REDIRECT_URI: "{{ .Values.configEnvs.microsoftRedirectUri }}"
  MICROSOFT_BASE_URL: "{{ .Values.configEnvs.microsoftBaseUrl }}"
  BUILD_FLAG_IS_CLIENT: "{{ .Values.configEnvs.buildFlagIsClient }}"
  SMS_USERNAME: "{{ .Values.configEnvs.smsUsername }}"
  SMS_TOKEN: "{{ .Values.configEnvs.smsToken }}"
  MOVILE_URL: "{{ .Values.configEnvs.movilleUrl }}"
  VERIFY_TOKEN: "{{ .Values.configEnvs.verifyToken }}"
  INTERNAL_CHAT_JWT: "{{ .Values.configEnvs.internalChatJwt }}"
  INTERNAL_CHAT_URL: "{{ .Values.configEnvs.internalChatUrl }}"
  HUB360_API_USERNAME: "{{ .Values.configEnvs.hub360Username }}"
  HUB360_API_PASSWORD: "{{ .Values.configEnvs.hub360Password }}"
  HUB360_API_PARTNER_ID: "{{ .Values.configEnvs.hub360PartnerId }}"
  MINUTES_TO_ALERT_WEBHOOK_INACTIVATION: "{{ .Values.configEnvs.minutesToAlertWebhookInactivation }}"
  MINUTES_FOR_WEBHOOK_INACTIVATION: "{{ .Values.configEnvs.minutesForWebhookInactiovation }}"
  ALLOW_IGNORE_STRENGTH_PASSOWRD: "{{ .Values.configEnvs.allowIgnoreStrengthPassword }}"
  WA_HEADFULL: "{{ .Values.configEnvs.waHeadFull }}"
  WHATSAPP_BUSINESS_WEBHOOK_PROCESS_RECEIVED_FILE_QUEUE_CONCURRENCY: "{{ .Values.configEnvs.whatsappBusinessWebhookProcessReceivedFileQueueConcurrency }}"
  USE_CACHED_TICKETS_COUNT: "{{ .Values.configEnvs.useCachedTicketsCount }}"
  REFRESH_TICKET_COUNT_CRON_EXPRESSION: "{{ .Values.configEnvs.refreshTicketCountCronExpression }}"
  NTBA_FIX_319: "{{ .Values.configEnvs.ntbaFix319 }}"
  FILE_CACHE_TTL: "{{ .Values.configEnvs.fileCacheTtl }}"
  EXTERNAL_BROWSER: "{{ .Values.configEnvs.externalBrowser }}"
  BROWSER_WS_ENDPOINT_PDF_GENERATOR: "{{ .Values.configEnvs.browserWsEndpointPdfGenerator }}"
  API_URL: "{{ .Values.configEnvs.apiUrl }}"
  SOCKET_URL: "{{ .Values.configEnvs.socketUrl }}"
  MEDIA_HOST: "{{ .Values.configEnvs.mediaHost }}"
  BETA: "{{ .Values.configEnvs.beta }}"
  CLIENT_ID: "{{ .Values.configEnvs.clientId }}"
  CLIENT_SECRET: "{{ .Values.configEnvs.clientSecret }}"
  GOOGLE_API_KEY: "{{ .Values.configEnvs.googleApiKey }}"
  GOOGLE_ANALYTICS_TAG: "{{ .Values.configEnvs.googleAnalyticsTag }}"
  UPDATE_FETCH_COUNT_DEBOUNCE: "{{ .Values.configEnvs.updateFetchCountDebounce }}"
  PROXY_URL: "{{ .Values.configEnvs.proxyUrl }}"
  INTERNAL_API_URL: "{{ .Values.configEnvs.internalApiUrl }}"
  TYPE: "{{ .Values.configEnvs.type }}"
  BROWSER_WS_ENDPOINT: "{{ .Values.configEnvs.browserWsEndpoint }}"
  SOCKET_GATEWAY_ADDRESS: "{{ .Values.configEnvs.socketGatewayAddress }}"
  SERVER_POD_URL: "{{ .Values.configEnvs.serverpodUrl }}"
  ONE_SIGNAL_URL: "{{ .Values.configEnvs.oneSignalUrl }}"
  ONE_SIGNAL_APP_ID: "{{ .Values.configEnvs.oneSignalAppId }}"
  SENDGRID_API_KEY: "{{ .Values.configEnvs.sendgridApiKey }}"
  NODE_TLS_REJECT_UNAUTHORIZED: "{{ .Values.configEnvs.nodeTlsRejectUnauthorized }}"
  CHROME_EXECUTABLE_PATH: "{{ .Values.configEnvs.chromeExecutablePath }}"
  WA_SOCKET_RESTARTER_INTERVAL: "{{ .Values.configEnvs.waSocketRestarterInterval }}"
  WA_BRUTE_FORCE_MESSAGE_CHECKER_INTERVAL: "{{ .Values.configEnvs.waBruteForceMessageCheckerInterval }}"
  WA_VERSION: "{{ .Values.configEnvs.waVersion }}"
  FACEBOOK_API_URL: "{{ .Values.configEnvs.facebookApiUrl }}"
  FACEBOOK_APP_ID: "{{ .Values.configEnvs.facebookAppId }}"
  FACEBOOK_CLIENT_SECRET: "{{ .Values.configEnvs.facebookClientSecret }}"
  TELEGRAM_PROFILE_URL: "{{ .Values.configEnvs.telegramProfileUrl }}"
  TELEGRAM_WEBHOOK_URL: "{{ .Values.configEnvs.telegramWebhookUrl }}"
  POSITUS_USERNAME: "{{ .Values.configEnvs.positusUsername }}"
  POSITUS_PASSWORD: "{{ .Values.configEnvs.positusPassword }}"
  MONGODB_HOST: "{{ .Values.configEnvs.mongoDbHost }}"
  MONGODB_PORT: "{{ .Values.configEnvs.mongoDbPort }}"
  MONGODB_DATABASE: "{{ .Values.configEnvs.mongoDbDatabase }}"
  MONGODB_USERNAME: "{{ .Values.configEnvs.mongoDbUsername }}"
  MONGODB_PASSWORD: "{{ .Values.configEnvs.mongoDbPassword }}"
  MONGODB_MAX_POOL_SIZE: "{{ .Values.configEnvs.mongoDbMaxPoolSize }}"
  MONGODB_AUTH_SOURCE: "{{ .Values.configEnvs.mongoDbAuthSource }}"
  WORKERS_PORT: "{{ .Values.configEnvs.workersPort }}"
  MICROSOFT_LOGIN_BASE_URL: "{{ .Values.configEnvs.microsoftLoginBaseUrl }}"
  GUPSHUP_EMAIL: "{{ .Values.configEnvs.gupshupEmail }}"
  GUPSHUP_PASSWORD: "{{ .Values.configEnvs.gupshupPassword }}"
  EXPO_ACCESS_TOKEN: "{{ .Values.configEnvs.expoAccessToken }}"
