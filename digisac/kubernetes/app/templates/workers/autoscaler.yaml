apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: app-workers-autoscaler
  namespace: {{ .Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: app-workers-deployment
  minReplicas: {{ .Values.autoscaler.workers.min }}
  maxReplicas: {{ .Values.autoscaler.workers.max }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
