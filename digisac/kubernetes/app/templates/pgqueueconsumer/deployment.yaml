apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: app-pgqueueconsumer
    service: pgqueueconsumer
    configmap: app-config-envs
  name: app-pgqueueconsumer-deployment
  namespace: {{ .Values.namespace }}
spec:
  selector:
    matchLabels:
      app: app-pgqueueconsumer
  template:
    metadata:
      labels:
        app: app-pgqueueconsumer
      annotations:
        co.elastic.logs/json.add_error_key: "true"
        co.elastic.logs/json.expand_keys: "true"
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.overwrite_keys: "true"
    spec:
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: app-pgqueueconsumer
      containers:
      - command:
        - /bin/sh
        - -c
        - exec node dist/microServices/pgQueueConsumer
        envFrom:
        - configMapRef:
            name: app-config-envs
        env:
          - name: PG_QUEUE_CONSUMER_PORT
            value: "{{ .Values.pgQueue.consumerPort }}"
          - name: PG_QUEUE_CONCURRENCY
            value: "{{ .Values.pgQueue.concurrency }}"
          - name: PG_QUEUE_MAX_POOL_SIZE
            value: "{{ .Values.pgQueue.maxPoolSize }}"
          - name: PG_QUEUE_POLL_INTERVAL
            value: "{{ .Values.pgQueue.poolInterval }}"
          - name: PG_QUEUE_LOGS
            value: "{{ .Values.pgQueue.logs }}"
          - name: DB_HOST
            valueFrom:
              configMapKeyRef:
                name: app-config-envs
                key: DB_HOST
        image: {{ .Values.imagePgqueueconsumer }}
        imagePullPolicy: "IfNotPresent"
        name: app-pgqueueconsumer
        resources:
          limits:
            cpu: {{ .Values.resourceLimits.pgqueueconsumer.limCpu }}
            memory: {{ .Values.resourceLimits.pgqueueconsumer.limMem }}
          requests:
            cpu: {{ .Values.resourceLimits.pgqueueconsumer.reqCpu }}
            memory: {{ .Values.resourceLimits.pgqueueconsumer.reqMem }}
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 20
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}

