apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: app-pgqueueconsumer-autoscaler
  namespace: {{ .Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: app-pgqueueconsumer-deployment
  minReplicas: {{ .Values.autoscaler.pgqueueconsumer.min }}
  maxReplicas: {{ .Values.autoscaler.pgqueueconsumer.max }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
