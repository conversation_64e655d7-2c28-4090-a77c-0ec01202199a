apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    configmap.reloader.stakater.com/reload: app-config-envs
  labels:
    app: app-front
    service: front
    configmap: app-config-envs
  name: app-front-deployment
  namespace: {{ .Values.namespace }}
spec:
  selector:
    matchLabels:
      app: app-front
  template:
    metadata:
      annotations:
        linkerd.io/inject: enabled
      labels:
        app: app-front
    spec:
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: app-front
      containers:
      - args:
        - dist/server
        command:
        - node
        envFrom:
        - configMapRef:
            name: app-config-envs
        env:
        - name: PORT
          value: "8080"
        - name: SENTRY_DSN
          value: "{{ .Values.sentryDsnFront }}"
        image: {{ .Values.imageFront }}
        imagePullPolicy: "IfNotPresent"
        name: app-front
        ports:
        - containerPort: 8080
          protocol: TCP
        resources:
          limits:
            cpu: {{ .Values.resourceLimits.front.limCpu }}
            memory: {{ .Values.resourceLimits.front.limMem }}
          requests:
            cpu: {{ .Values.resourceLimits.front.reqCpu }}
            memory: {{ .Values.resourceLimits.front.reqMem }}
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 20
      tolerations:
        - key: "oci.oraclecloud.com/oke-is-preemptible"
          operator: "Exists"
          effect: "NoSchedule"
      affinity:
        {{- if .Values.statelessNodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.statelessNodeAffinity.key }}
                operator: In
                values: {{ .Values.statelessNodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
