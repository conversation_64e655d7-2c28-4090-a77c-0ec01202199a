apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: app-queueconsumer
    configmap: app-config-envs
    service: queueconsumer
  name: app-queueconsumer-deployment
  namespace: {{ .Values.namespace }}
spec:
  selector:
    matchLabels:
      app: app-queueconsumer
  template:
    metadata:
      labels:
        app: app-queueconsumer
      annotations:
        co.elastic.logs/json.add_error_key: "true"
        co.elastic.logs/json.expand_keys: "true"
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.overwrite_keys: "true"
    spec:
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: app-queueconsumer
      containers:
      - command:
        - /bin/sh
        - -c
        - exec node dist/microServices/queueConsumer
        envFrom:
        - configMapRef:
            name: app-config-envs
        env:
          - name: QUEUE_CONSUMER_PREFETCH_COUNT
            value: "{{ .Values.queueConsumer.prefetchCount }}"
        image: {{ .Values.imageQueueconsumer }}
        imagePullPolicy: "IfNotPresent"
        name: app-queueconsumer
        resources:
          limits:
            cpu: {{ .Values.resourceLimits.queueconsumer.limCpu }}
            memory: {{ .Values.resourceLimits.queueconsumer.limMem }}
          requests:
            cpu: {{ .Values.resourceLimits.queueconsumer.reqCpu }}
            memory: {{ .Values.resourceLimits.queueconsumer.reqMem }}
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 20
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
