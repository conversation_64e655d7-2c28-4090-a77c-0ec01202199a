apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: app-queueconsumer-autoscaler
  namespace: {{ .Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: app-queueconsumer-deployment
  minReplicas: {{ .Values.autoscaler.queueconsumer.min }}
  maxReplicas: {{ .Values.autoscaler.queueconsumer.max }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
