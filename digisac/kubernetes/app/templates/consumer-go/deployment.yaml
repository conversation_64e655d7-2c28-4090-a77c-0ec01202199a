apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: app-consumers-go
    service: consumers-go
  name: app-consumers-go-deployment
  namespace: {{ .Values.namespace }}
spec:
  selector:
    matchLabels:
      app: app-consumers-go
  template:
    metadata:
      labels:
        app: app-consumers-go
      annotations:
        co.elastic.logs/json.add_error_key: "true"
        co.elastic.logs/json.expand_keys: "true"
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.overwrite_keys: "true"
    spec:
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: app-consumers-go
      containers:
      - command:
        - /app/consumer/consumer
        env:
          - name: SEND_MESSAGE_QUEUE_CONSUMERS
            value: "10"
          - name: WEBHOOKS_CONSUMERS
            value: "10"
          - name: PULSAR_URL
            value: "pulsar://broker:6650"
        image: {{ .Values.imageWorkersGo }}
        imagePullPolicy: "IfNotPresent"
        name: app-consumers-go
        resources:
          limits:
            cpu: {{ .Values.resourceLimits.workers.limCpu }}
            memory: {{ .Values.resourceLimits.workers.limMem }}
          requests:
            cpu: {{ .Values.resourceLimits.workers.reqCpu }}
            memory: {{ .Values.resourceLimits.workers.reqMem }}
        ports:
        - containerPort: 8080
          protocol: TCP
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.statelessNodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}