apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: app-consumers-go-autoscaler
  namespace: {{ .Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: app-consumers-go-deployment
  minReplicas: {{ .Values.autoscaler.workers.min }}
  maxReplicas: {{ .Values.autoscaler.workers.max }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
