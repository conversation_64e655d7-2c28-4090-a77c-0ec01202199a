apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: app-events-autoscaler
  namespace: {{ .Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: app-events-deployment
  minReplicas: {{ .Values.autoscaler.events.min }}
  maxReplicas: {{ .Values.autoscaler.events.max }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70