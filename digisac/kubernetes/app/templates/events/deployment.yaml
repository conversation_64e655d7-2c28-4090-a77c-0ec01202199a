apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: app-events
    service: events
    configmap: app-config-envs
  name: app-events-deployment
  namespace: {{ .Values.namespace }}
spec:
  selector:
    matchLabels:
      app: app-events
  template:
    metadata:
      labels:
        app: app-events
      annotations:
        co.elastic.logs/json.add_error_key: "true"
        co.elastic.logs/json.expand_keys: "true"
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.overwrite_keys: "true"
    spec:
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: app-events
      containers:
      - command:
        - /bin/sh
        - -c
        - exec node dist/microServices/events
        envFrom:
        - configMapRef:
            name: app-config-envs
        image: {{ .Values.imageEvents }}
        imagePullPolicy: "IfNotPresent"
        name: app-events
        resources:
          limits:
            cpu: {{ .Values.resourceLimits.events.limCpu }}
            memory: {{ .Values.resourceLimits.events.limMem }}
          requests:
            cpu: {{ .Values.resourceLimits.events.reqCpu }}
            memory: {{ .Values.resourceLimits.events.reqMem }}
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 20
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
