apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: app-cron
    service: cron
    configmap: app-config-envs
  name: app-cron-statefulset
  namespace: {{ .Values.namespace }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: app-cron
  serviceName: app-cron
  template:
    metadata:
      labels:
        app: app-cron
      annotations:
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.overwrite_keys: "true"
        co.elastic.logs/json.add_error_key: "true"
        co.elastic.logs/json.expand_keys: "true"
    spec:
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: app-cron
      containers:
      - command:
        - /bin/sh
        - -c
        - exec node dist/microServices/cron
        envFrom:
        - configMapRef:
            name: app-config-envs
        image: {{ .Values.imageCron }}
        imagePullPolicy: "IfNotPresent"
        name: app-cron
        ports:
        - containerPort: 8080
          protocol: TCP
        resources:
          limits:
            cpu: {{ .Values.resourceLimits.cron.limCpu }}
            memory: {{ .Values.resourceLimits.cron.limMem }}
          requests:
            cpu: {{ .Values.resourceLimits.cron.reqCpu }}
            memory: {{ .Values.resourceLimits.cron.reqMem }}
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 20
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
