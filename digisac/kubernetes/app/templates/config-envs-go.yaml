apiVersion: v1
kind: ConfigMap
metadata:
  name: app-go-config-envs
  annotations:
    reloader.stakater.com/match: "true"
  namespace: {{ .Values.namespace }}
data:
  # Api
  PUBLIC_URL: "https://app.digisac.io/v1"

  # Wall Api (Whatsapp)
  WHATSAPP_API_HTTP_ADDRESS: "http://wall-api-1:5060"

  # Nao usar "http://" pois é usado em conexões GRPC
  WHATSAPP_API_GRPC_ADDRESS: "wall-api-1:50063"

  WHATSAPP_API_TOKEN: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjIyMDYxMjQ5NjksInN1YiI6ImE4ZmFlNDIxLWM4NzMtNDc2ZC04MzJkLWQ3NmM5NDFjM2Q5NSJ9.GoZ4xEiOJOwbBycooiKe0op6MTP6AghdJppmP2du48E"

  # Worker
  WORKERS_GO_PORT: "8400"

  # RabbitMQ
  EVENTS_CHANNELS_COUNT: "20"

  # Postgresql DB
  DB_HOST: "{{ .Values.configEnvs.dbHost }}"
  DB_NAME: "{{ .Values.configEnvs.dbDatabase }}"
  DB_USERNAME: "{{ .Values.configEnvs.dbUsername }}"
  DB_PASSWORD: "{{ .Values.configEnvs.dbPassword }}"
  DB_PORT: "{{ .Values.configEnvs.dbPort }}"
  DB_MAX_CONNS: "4000"
  DB_LOG: "true"

  # Pulsar
  PULSAR_URL: "pulsar://broker:6650"

  # Logs
  LOG_TYPE: "json"

  # Crypto
  ENCRYPTION_KEY: "cd6fb4179d7e818a2674d0b3d5fbe4cd79ea48a30470d5346486494ececd113c"

  # Storage (s3 ou oracle, default é oracle)
  STORAGE_DRIVER: "oracle"

  # Aws
  AWS_ACCESS_KEY_ID: "********************"
  AWS_SECRET_ACCESS_KEY: "lwkXoFLwGjciJSQWgunlqfZmiq0VaNHnOep6Gt1p"
  AWS_BUCKET_NAME: "mandeumzap-storage-test"
  AWS_REGION: "us-east-1"

  ORACLE_ACCESS_KEY_ID: "afc6c56f17a156a556e206c497e46fca9f07f942"
  ORACLE_SECRET_ACCESS_KEY: "bhq1TLyajauqZI14IvXG+3FVybywvoDNEdYHYwXVvMI: "
  ORACLE_BUCKETS_NAMES: "digisac-storage,digisac-storage-2,digisac-storage-3,digisac-storage-4,digisac-storage-5,digisac-storage-6,digisac-storage-7,digisac-storage-8,digisac-storage-9,digisac-storage-10"
  ORACLE_BUCKET_NAME_FALLBACK: "digisac-storage-test-fallback"
  ORACLE_REGION: "sa-vinhedo-1"
  ORACLE_ENDPOINT: "https://axvaplbwrlcl.compat.objectstorage.sa-vinhedo-1.oraclecloud.com"
