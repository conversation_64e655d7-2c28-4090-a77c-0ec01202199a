apiVersion: apps/v1
kind: Deployment
metadata:
  name: message-broker-producer
  namespace: {{ .Values.namespace }}
  labels:
    app: message-broker-producer
    version: v1
spec:
  replicas: {{ .Values.autoscaler.messageBrokerProducer.min }}
  selector:
    matchLabels:
      app: message-broker-producer
      version: v1
  template:
    metadata:
      labels:
        app: message-broker-producer
        version: v1
    spec:
      containers:
      - name: message-broker-producer
        image: {{ .Values.imageMessageBrokerProducer }}
        ports:
        - containerPort: 2000
        env:
        - name: LOG_LEVEL
          value: "debug"
        - name: RABBITMQ_HOST
          value: "lavinmq.tools.svc.cluster.local"
        resources:
          requests:
            memory: {{ .Values.resourceLimits.messageBrokerProducer.reqMem }}
            cpu: {{ .Values.resourceLimits.messageBrokerProducer.reqCpu }}
          limits:
            memory: {{ .Values.resourceLimits.messageBrokerProducer.limMem }}
            cpu: {{ .Values.resourceLimits.messageBrokerProducer.limCpu }}
        # livenessProbe:
        #   httpGet:
        #     path: /health
        #     port: 2000
        #   initialDelaySeconds: 30
        #   periodSeconds: 10
        #   timeoutSeconds: 5
        #   failureThreshold: 3
        # readinessProbe:
        #   httpGet:
        #     path: /health
        #     port: 2000
        #   initialDelaySeconds: 5
        #   periodSeconds: 5
        #   timeoutSeconds: 3
        #   failureThreshold: 3
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
