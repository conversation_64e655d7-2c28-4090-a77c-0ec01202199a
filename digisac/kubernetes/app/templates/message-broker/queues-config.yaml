apiVersion: v1
kind: ConfigMap
metadata:
  name: message-broker-queues-config
  namespace: app
data:
  config.yaml: |
    defaults:
      max-retries: 25
      processing-timeout-seconds: 60
      mode: concurrent
      consumers-per-instance: 10 # for mode:concurrent
      partitions: 10 # for mode:sequential
      handler: main-workers

    handlers:
      main-workers:
        url: http://app-workers.app.svc.cluster.local:80/run/:queueName?wrapped=1

      main-workers-go:
        url: http://app-workers-go.app.svc.cluster.local:8000/run/:queueName?wrapped=1

    queues:
      webhook-sender:
        topics:
          - 'message.*'
          - 'ticket.created'
          - 'ticket.updated'
          - 'ticket.destroyed'
          - 'service.*'
          - 'contact.*'
          - 'people.*'
          - 'organization.*'
          - 'user.*'
          - 'department.*'
          - 'ticketTopics.*'
          - 'role.*'
          - 'bot.*'
          - 'quickReplies.*'
          - 'tag.*'
          - 'campaign.*'
          - 'whatsapp.message.*'

      socket-sender:
        mode: concurrent
        consumers-per-instance: 10
        topics:
          - 'account.updated'
          - 'contact.created'
          - 'contact.updated'
          - 'contact.destroyed'
          - 'ticket.created'
          - 'ticket.updated'
          - 'ticket.destroyed'
          - 'ticket.opened'
          - 'ticket.transfer'
          - 'ticket.closed'
          - 'service.created'
          - 'service.updated'
          - 'service.destroyed'
          - 'user.updated'
          - 'user.took_over'
          - 'tag.created'
          - 'tag.updated'
          - 'tag.destroyed'
          - 'notifications.created'
          - 'notifications.updated'
          - 'campaign.created'
          - 'campaign.updated'
          - 'campaign.destroyed'
          - 'message.created'
          - 'message.updated'
          - 'pipeline.created'
          - 'pipeline.updated'
          - 'pipeline.destroyed'
          - 'card.created'
          - 'card.updated'
          - 'card.destroyed'

      service-events:
        topics:
          - 'service.created'
          - 'service.updated'
          - 'service.destroyed'

      agnus-my-plan-update:
        topics:
          - 'service.created'
          - 'service.archived'
          - 'service.destroyed'
          - 'user.created'
          - 'user.archived'
          - 'user.destroyed'

      bot:
        mode: sequential
        max-retries: 10
        processing-timeout-seconds: 30
        topics:
          - 'message.created'
          - 'message.widget_message_received'
          - 'message.user_message_sent'
          - 'ticket.opened'
          - 'ticket.closed'
          - 'ticket.inactive'
          - 'contact.anonymous-user-created-webchat'
          - 'contact.init-chat-webchat'
          - 'contact.created'
          - 'bot.api_signal'
          - 'bot'
          - 'bot.inactive.no-model'

      notification:
        topics:
          - 'message.created'

      expo-notification:
        topics:
          - 'message.created'

      block-message-rule:
        topics:
          - 'blockMessageRule.blocked-by-message-rule'

      queued-whatsapp-business-webhook:
        mode: sequential
        topics:
          - 'queued-whatsapp-business-webhook'

      queued-whatsapp-business-process-received-file:
        mode: sequential
        topics:
          - 'queued-whatsapp-business-process-received-file'

      queued-whatsapp-business-send-message-to-broker:
        mode: sequential
        topics:
          - 'queued-whatsapp-business-send-message-to-broker'

      queued-whatsapp-business-status:
        mode: sequential
        max-retries: 7
        topics:
          - 'queued-whatsapp-business-status'

      queued-facebook-messenger-webhook:
        mode: sequential
        topics:
          - 'queued-facebook-messenger-webhook'

      queued-facebook-messenger-process-received-file:
        mode: sequential
        topics:
          - 'queued-facebook-messenger-process-received-file'

      queued-facebook-messenger-send-message-to-broker:
        mode: sequential
        topics:
          - 'queued-facebook-messenger-send-message-to-broker'

      stored-whatsapp-remote-rpc:
        mode: sequential
        topics:
          - 'stored-whatsapp-remote-rpc'

      low-level-webhook-sender:
        mode: sequential
        topics:
          - 'low-level-webhook-sender'

      send-webhook:
        mode: sequential
        topics:
          - 'send-webhook'

      queued-import-contact:
        mode: sequential
        max-retries: 3
        partitions: 1
        topics:
          - 'queued-import-contact'

      queued-save-contact-block-list:
        mode: sequential
        topics:
          - 'queued-save-contact-block-list'

      queued-prepare-contact-block-or-unblock:
        mode: sequential
        max-retries: 9
        topics:
          - 'queued-prepare-contact-block-or-unblock'

      queued-contact-block-or-unblock:
        mode: sequential
        topics:
          - 'queued-contact-block-or-unblock'

      queued-google-business-message-webhook:
        mode: sequential
        topics:
          - 'queued-google-business-message-webhook'

      queued-google-business-message-send-message-to-broker:
        mode: sequential
        topics:
          - 'queued-google-business-message-send-message-to-broker'

      queued-google-business-message-process-received-file:
        mode: sequential
        topics:
          - 'queued-google-business-message-process-received-file'

      queued-apply-distribution-rule:
        mode: sequential
        max-retries: 3
        partitions: 1
        topics:
          - 'queued-apply-distribution-rule'

      audio-metadata:
        mode: sequential
        topics:
          - 'audio-metadata'

      queued-reclame-aqui-send-message-to-broker:
        mode: sequential
        topics:
          - 'queued-reclame-aqui-send-message-to-broker'

      queued-reclame-aqui-sync-messages:
        mode: sequential
        partitions: 1
        topics:
          - 'queued-reclame-aqui-sync-messages'

      queue-attach-contact-to-pipeline:
        mode: sequential
        topics:
          - 'queue-attach-contact-to-pipeline'

      update-contact-plan:
        mode: sequential
        topics:
          - 'update-contact-plan'

      bot-messages:
        mode: sequential
        max-retries: 3
        topics:
          - 'bot-messages'

      queued-audio-transcribe:
        mode: sequential
        max-retries: 3
        topics:
          - 'queued-audio-transcribe'

      queued-summary:
        mode: sequential
        max-retries: 3
        topics:
          - 'queued-summary'

      queued-csat-feedback-score:
        mode: concurrent
        max-retries: 1
        topics:
          - 'queued-csat-feedback-score'

      queued-start-summary:
        mode: sequential
        max-retries: 4
        topics:
          - 'queued-start-summary'

      #### NOVO SISTEMA DE MENSAGERIA NO WORKERS-GO ####
      send-message-to-broker:
        mode: sequential
        handler: main-workers-go
        max-retries: 25 # 4 d 4 h 25 min 33.8 s
        partitions: 5
        topics:
          - 'send-message-to-broker'

      receive-message-webhook:
        mode: sequential
        handler: main-workers-go
        max-retries: 25 # 4 d 4 h 25 min 33.8 s
        partitions: 5
        topics:
          - 'receive-message-webhook'

      receive-status-webhook:
        mode: sequential
        handler: main-workers-go
        max-retries: 10 # 9 h 40 min 33.8 s 
        partitions: 15
        topics:
          - 'receive-status-webhook'

      receive-service-webhook:
        mode: sequential
        handler: main-workers-go
        max-retries: 3 # 30.2 s 
        partitions: 3
        topics:
          - 'receive-service-webhook'

      receive-template-webhook:
        mode: sequential
        handler: main-workers-go
        max-retries: 3 # 30.2 s
        partitions: 3
        topics:
          - 'receive-template-webhook'

      receive-media:
        mode: sequential
        handler: main-workers-go
        max-retries: 25 # 4 d 4 h 25 min 33.8 s
        partitions: 5
        topics:
          - 'receive-media'
