apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: message-broker-producer-hpa
  namespace: {{ .Values.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: message-broker-producer
  minReplicas: {{ .Values.autoscaler.messageBrokerProducer.min }}
  maxReplicas: {{ .Values.autoscaler.messageBrokerProducer.max }}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
