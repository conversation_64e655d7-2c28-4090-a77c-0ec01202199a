apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: app-browserless
    configmap: app-config-envs
    service: app-browserless
  name: app-browserless-statefulset
  namespace: {{ .Values.namespace }}
spec:
  selector:
    matchLabels:
      app: app-browserless
  serviceName: app-browserless
  template:
    metadata:
      labels:
        app: app-browserless
      annotations:
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.overwrite_keys: "true"
        co.elastic.logs/json.add_error_key: "true"
        co.elastic.logs/json.expand_keys: "true"
    spec:
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: app-browserless
      containers:
      - command:
        - /bin/sh
        - -c
        - (rm /usr/src/app/wp-sessions/*/Singleton* || true) && ./start.sh
        env:
        - name: MAX_CONCURRENT_SESSIONS
          value: 1000
        - name: CONNECTION_TIMEOUT
          value: -1
        - name: DEFAULT_LAUNCH_ARGS
          value: "[\"--disable-web-security\"]"
        - name: DEFAULT_IGNORE_HTTPS_ERRORS
          value: "true"
        envFrom:
        - configMapRef:
            name: app-config-envs
        image: {{ .Values.imageBrowserless }}
        imagePullPolicy: "IfNotPresent"
        name: app-browserless
        ports:
        - containerPort: 3000
          protocol: TCP
        resources:
          limits:
            cpu: {{ .Values.resourceLimits.browserless.limCpu }}
            memory: {{ .Values.resourceLimits.browserless.limMem }}
          requests:
            cpu: {{ .Values.resourceLimits.browserless.reqCpu }}
            memory: {{ .Values.resourceLimits.browserless.reqMem }}
        # readinessProbe:
        #   httpGet:
        #     path: /health
        #     port: 80
        #   initialDelaySeconds: 5
        #   periodSeconds: 20
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
