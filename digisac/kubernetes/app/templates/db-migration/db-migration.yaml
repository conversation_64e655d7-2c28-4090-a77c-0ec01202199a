apiVersion: batch/v1
kind: Job
metadata:
    name: db-migrate
    namespace: {{ .Values.namespace }}
spec:
  activeDeadlineSeconds: 10000
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
    spec:
      containers:
      - command:
        - /bin/sh
        - -c
        - node ./node_modules/.bin/sequelize db:migrate --debug
        envFrom:
        - configMapRef:
            name: app-config-envs
        image: {{ .Values.imageDbMigration }}
        imagePullPolicy: "IfNotPresent"
        name: db-migrate
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
      restartPolicy: Never
  ttlSecondsAfterFinished: 100
