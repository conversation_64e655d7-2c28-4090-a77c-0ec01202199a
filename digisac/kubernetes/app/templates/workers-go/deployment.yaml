apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-workers-go
  namespace: {{ .Values.namespace }}
  labels:
    app: app-workers-go
    version: v1
spec:
  replicas: {{ .Values.autoscaler.workersGo.min }}
  selector:
    matchLabels:
      app: app-workers-go
      version: v1
  template:
    metadata:
      labels:
        app: app-workers-go
        version: v1
    spec:
      containers:
      - name: app-workers-go
        image: {{ .Values.imageWorkersGo }}

        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: app-workers-go-config-envs
        resources:
          requests:
            memory: {{ .Values.resourceLimits.workersGo.reqMem }}
            cpu: {{ .Values.resourceLimits.workersGo.reqCpu }}
          limits:
            memory: {{ .Values.resourceLimits.workersGo.limMem }}
            cpu: {{ .Values.resourceLimits.workersGo.limCpu }}
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}
      affinity:
        {{- if .Values.nodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.nodeAffinity.key }}
                operator: In
                values: {{ .Values.nodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
