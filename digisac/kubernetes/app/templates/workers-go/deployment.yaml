apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    configmap.reloader.stakater.com/reload: app-go-config-envs
  labels:
    app: app-workers-go
    service: workers-go
    configmap: app-go-config-envs
  name: app-workers-go-deployment
  namespace: {{ .Values.namespace }}
spec:
  selector:
    matchLabels:
      app: app-workers-go
  template:
    metadata:
      labels:
        app: app-workers-go
      annotations:
        co.elastic.logs/json.add_error_key: "true"
        co.elastic.logs/json.expand_keys: "true"
        co.elastic.logs/json.keys_under_root: "true"
        co.elastic.logs/json.overwrite_keys: "true"
    spec:
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: app-workers-go
      containers:
      - command:
        - /app/worker/worker
        envFrom:
        - configMapRef:
            name: app-go-config-envs
        image: {{ .Values.imageWorkersGo }}
        imagePullPolicy: "IfNotPresent"
        name: app-workers-go
        resources:
          limits:
            cpu: {{ .Values.resourceLimits.workers.limCpu }}
            memory: {{ .Values.resourceLimits.workers.limMem }}
          requests:
            cpu: {{ .Values.resourceLimits.workers.reqCpu }}
            memory: {{ .Values.resourceLimits.workers.reqMem }}
        ports:
        - containerPort: 8080
          protocol: TCP
      tolerations:
        - key: "oci.oraclecloud.com/oke-is-preemptible"
          operator: "Exists"
          effect: "NoSchedule"
      affinity:
        {{- if .Values.statelessNodeAffinity }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{ .Values.statelessNodeAffinity.key }}
                operator: In
                values: {{ .Values.statelessNodeAffinity.value | toYaml | nindent 16 }}
        {{- end }}
      imagePullSecrets:
      - name: {{ .Values.registryCredentialName }}