apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql
  namespace: {{ .Values.namespace }}
  labels:
    app: postgresql
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: postgresql
  template:
    metadata:
      labels:
        app: postgresql
    spec:
      nodeSelector:
        {{- toYaml .Values.nodeSelector | nindent 8 }}
      tolerations:
        {{- toYaml .Values.tolerations | nindent 8 }}
      containers:
        - name: postgresql
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          {{- if .Values.configFiles.enabled }}
          command:
            {{- toYaml .Values.command | nindent 12 }}
          {{- end }}
          ports:
            - name: postgres
              containerPort: 5432
              protocol: TCP
          env:
            - name: POSTGRES_USER
              value: "{{ .Values.env.POSTGRES_USER }}"
            - name: POSTGRES_PASSWORD
              value: "{{ .Values.env.POSTGRES_PASSWORD }}"
            - name: POSTGRES_DB
              value: "{{ .Values.env.POSTGRES_DB }}"
            - name: PGDATA
              value: "/var/lib/postgresql/data/pgdata"
          volumeMounts:
            - name: postgresql-data
              mountPath: /var/lib/postgresql/data
            {{- if .Values.configFiles.enabled }}
            - name: postgresql-config
              mountPath: /etc/postgresql-config
              readOnly: true
            {{- end }}
            - name: dshm
              mountPath: /dev/shm
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- if .Values.healthcheck.enabled }}
          livenessProbe:
            exec:
              command:
                - pg_isready
                - -U
                - "{{ .Values.env.POSTGRES_USER }}"
            initialDelaySeconds: {{ .Values.healthcheck.initialDelaySeconds }}
            periodSeconds: {{ .Values.healthcheck.periodSeconds }}
            timeoutSeconds: {{ .Values.healthcheck.timeoutSeconds }}
            failureThreshold: {{ .Values.healthcheck.failureThreshold }}
          readinessProbe:
            exec:
              command:
                - pg_isready
                - -U
                - "{{ .Values.env.POSTGRES_USER }}"
            initialDelaySeconds: {{ .Values.healthcheck.initialDelaySeconds }}
            periodSeconds: {{ .Values.healthcheck.periodSeconds }}
            timeoutSeconds: {{ .Values.healthcheck.timeoutSeconds }}
            failureThreshold: {{ .Values.healthcheck.failureThreshold }}
          {{- end }}
      volumes:
        - name: postgresql-data
          {{- if .Values.persistence.enabled }}
          persistentVolumeClaim:
            claimName: postgresql-pvc
          {{- else }}
          emptyDir: {}
          {{- end }}
        {{- if .Values.configFiles.enabled }}
        - name: postgresql-config
          configMap:
            name: postgresql-config
        {{- end }}
        - name: dshm
          emptyDir:
            medium: Memory
            sizeLimit: {{ .Values.sharedMemory.size }}
      imagePullSecrets:
        - name: ocirsecret
