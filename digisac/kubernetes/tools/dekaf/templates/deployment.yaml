apiVersion: apps/v1
kind: Deployment
metadata:
  name: dekaf
  namespace: {{ .Values.namespace }}
  labels:
    app: dekaf
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: dekaf
  template:
    metadata:
      labels:
        app: dekaf
    spec:
      nodeSelector:
        pool: tools
      tolerations:
        - key: "pool"
          operator: "Equal"
          value: "tools"
          effect: "NoSchedule"
      containers:
        - name: dekaf
          image: "{{ .Values.image.repository }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: 8090
          envFrom:
            - configMapRef:
                name: dekaf-env
