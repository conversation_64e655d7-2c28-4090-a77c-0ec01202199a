apiVersion: apps/v1
kind: Deployment
metadata:
  name: pgbouncer
  namespace: {{ .Values.namespace }}
  labels:
    app: pgbouncer
spec:
  replicas: {{ .Values.replicas }}
  revisionHistoryLimit: 1  # removes old replicasets for deployment rollbacks
  strategy:
    rollingUpdate:
      maxUnavailable: 0  # Avoid Terminating and ContainerCreating at the same time
  selector:
    matchLabels:
      app: pgbouncer
  template:
    metadata:
      labels:
        app: pgbouncer
      annotations:
        cluster-autoscaler.kubernetes.io/safe-to-evict: "false"
    spec:
      nodeSelector:
        pool: tools
      tolerations:
        - key: "pool"
          operator: "Equal"
          value: "tools"
          effect: "NoSchedule"
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app: pgbouncer
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchLabels:
                  app: pgbouncer
              topologyKey: kubernetes.io/hostname
      containers:
        - name: pgbouncer
          image: {{ .Values.image }}  # Using specific version instead of latest
          #imagePullPolicy: Always
          ports:
            - containerPort: 5432
          env:
            - name: PGBOUNCER_POOL_MODE
              value: {{ .Values.poolMode }}
            - name: PGBOUNCER_SERVER_RESET_QUERY
              value: "{{ .Values.serverResetQuery }}"
            - name: PGBOUNCER_MAX_CLIENT_CONN
              value: "{{ .Values.maxClientConn }}"
            - name: PGBOUNCER_DEFAULT_POOL_SIZE
              value: "{{ .Values.defaultPollSize }}"
            - name: PGBOUNCER_RESERVE_POOL_SIZE
              value: "{{ .Values.reservePollSize }}"
            - name: PGBOUNCER_MAX_DB_CONNECTIONS
              value: "{{ .Values.maxDbConnections }}"
            - name: PGBOUNCER_PORT
              value: "5432"
            - name: POSTGRESQL_HOST
              value: "{{ .Values.dbHost }}"
            - name: POSTGRESQL_PASSWORD
              value: "{{ .Values.dbPassword }}"
            - name: POSTGRESQL_USERNAME
              value: "{{ .Values.dbUsername }}"
            # Add these recommended settings for Bitnami image
            - name: PGBOUNCER_DATABASE
              value: "mandeumzap"  # Connect to mandeumzap database
            - name: PGBOUNCER_AUTH_TYPE
              value: "md5"
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "256Mi"
              cpu: "500m"
          livenessProbe:
            tcpSocket:
              port: 5432
            periodSeconds: 60
            initialDelaySeconds: 30
          readinessProbe:
            tcpSocket:
              port: 5432
            periodSeconds: 10
            initialDelaySeconds: 5
          lifecycle:
            preStop:
              exec:
                # Allow existing queries clients to complete within 120 seconds
                command: ["/bin/sh", "-c", "pkill -INT pgbouncer || true && sleep 120"]
          securityContext:
            allowPrivilegeEscalation: false
            runAsNonRoot: true
            runAsUser: 1001
            capabilities:
              drop: ['all']