apiVersion: apps/v1
kind: Deployment
metadata:
  name: lavinmq
  namespace: {{ .Values.namespace }}
  labels:
    app: lavinmq
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: lavinmq
  template:
    metadata:
      labels:
        app: lavinmq
    spec:
      nodeSelector:
        {{- toYaml .Values.nodeSelector | nindent 8 }}
      tolerations:
        {{- toYaml .Values.tolerations | nindent 8 }}
      containers:
        - name: lavinmq
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: amqp
              containerPort: 5672
              protocol: TCP
            - name: management
              containerPort: 15672
              protocol: TCP
          env:
            - name: LAVINMQ_DEFAULT_USER
              value: "{{ .Values.env.LAVINMQ_DEFAULT_USER }}"
            - name: LAVINMQ_DEFAULT_PASS
              value: "{{ .Values.env.LAVINMQ_DEFAULT_PASS }}"
          volumeMounts:
            - name: lavinmq-data
              mountPath: /var/lib/lavinmq
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- if .Values.healthcheck.enabled }}
          livenessProbe:
            exec:
              command:
                - lavinmqctl
                - status
            initialDelaySeconds: {{ .Values.healthcheck.initialDelaySeconds }}
            periodSeconds: {{ .Values.healthcheck.periodSeconds }}
            timeoutSeconds: {{ .Values.healthcheck.timeoutSeconds }}
            failureThreshold: {{ .Values.healthcheck.failureThreshold }}
          readinessProbe:
            exec:
              command:
                - lavinmqctl
                - status
            initialDelaySeconds: {{ .Values.healthcheck.initialDelaySeconds }}
            periodSeconds: {{ .Values.healthcheck.periodSeconds }}
            timeoutSeconds: {{ .Values.healthcheck.timeoutSeconds }}
            failureThreshold: {{ .Values.healthcheck.failureThreshold }}
          {{- end }}
      volumes:
        - name: lavinmq-data
          {{- if .Values.persistence.enabled }}
          persistentVolumeClaim:
            claimName: lavinmq-pvc
          {{- else }}
          emptyDir: {}
          {{- end }}
      imagePullSecrets:
        - name: ocirsecret
