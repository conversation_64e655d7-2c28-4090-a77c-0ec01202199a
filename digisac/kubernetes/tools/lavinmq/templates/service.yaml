apiVersion: v1
kind: Service
metadata:
  name: lavinmq
  namespace: {{ .Values.namespace }}
  labels:
    app: lavinmq
spec:
  type: {{ .Values.service.type }}
  ports:
    - name: amqp
      port: {{ .Values.service.ports.amqp }}
      targetPort: 5672
      protocol: TCP
    - name: management
      port: {{ .Values.service.ports.management }}
      targetPort: 15672
      protocol: TCP
  selector:
    app: lavinmq
