## Components
components:
  # Core components
  zookeeper: true
  bookkeeper: true
  broker: true
  proxy: true
  autorecovery: true
  # Optional components
  toolset: false
  pulsar_manager: false  # Disable web UI by default for security
  prometheus: false
  grafana: false
  node_exporter: false

## Persistence
persistence:
  enabled: true
  storageClass: ""  # Use cluster default

## ZooKeeper settings
zookeeper:
  replicaCount: 3
  resources:
    requests:
      memory: "1Gi"
      cpu: "0.5"
    limits:
      memory: "2Gi"
      cpu: "1"
  configData:
    PULSAR_MEM: "-Xms1g -Xmx1g"
  nodeSelector:
    pool: tools
  tolerations:
    - key: "pool"
      operator: "Equal"
      value: "tools"
      effect: "NoSchedule"


## BookKeeper settings
bookkeeper:
  replicaCount: 3
  resources:
    requests:
      memory: "2Gi"
      cpu: "1"
    limits:
      memory: "4Gi"
      cpu: "2"
  configData:
    PULSAR_MEM: "-Xms2g -Xmx2g"
  volumes:
    journal:
      size: 10Gi
    ledgers:
      size: 50Gi
  nodeSelector:
    pool: tools
  tolerations:
    - key: "pool"
      operator: "Equal"
      value: "tools"
      effect: "NoSchedule"

## Broker settings
broker:
  replicaCount: 3
  resources:
    requests:
      memory: "2Gi"
      cpu: "1"
    limits:
      memory: "4Gi"
      cpu: "2"
  configData:
    PULSAR_MEM: "-Xms2g -Xmx2g"
  nodeSelector:
    pool: tools
  tolerations:
    - key: "pool"
      operator: "Equal"
      value: "tools"
      effect: "NoSchedule"

## Proxy settings
proxy:
  replicaCount: 2
  resources:
    requests:
      memory: "1Gi"
      cpu: "0.5"
    limits:
      memory: "2Gi"
      cpu: "1"
  nodeSelector:
    pool: tools
  tolerations:
    - key: "pool"
      operator: "Equal"
      value: "tools"
      effect: "NoSchedule"

## Security settings
security:
  tls:
    enabled: false  # Enable if you have proper certificates
  authentication:
    enabled: true
    provider: "jwt"
  authorization:
    enabled: true
  nodeSelector:
    pool: tools
  tolerations:
    - key: "pool"
      operator: "Equal"
      value: "tools"
      effect: "NoSchedule"

## Monitoring
prometheus:
  resources:
    requests:
      memory: "2Gi"
      cpu: "0.5"
    limits:
      memory: "4Gi"
      cpu: "1"
  nodeSelector:
    pool: tools
  tolerations:
    - key: "pool"
      operator: "Equal"
      value: "tools"
      effect: "NoSchedule"

grafana:
  resources:
    requests:
      memory: "512Mi"
      cpu: "0.2"
    limits:
      memory: "1Gi"
      cpu: "0.5"
  nodeSelector:
    pool: tools
  tolerations:
    - key: "pool"
      operator: "Equal"
      value: "tools"
      effect: "NoSchedule"

## Anti-affinity for high availability
antiAffinity:
  host:
    enabled: true
    type: "hard"  # Ensures pods are scheduled on different nodes

## Service settings
service:
  type: ClusterIP  # Assuming you're using Istio for ingress

## Additional tools settings
toolset:
  resources:
    requests:
      memory: "512Mi"
      cpu: "0.2"
    limits:
      memory: "1Gi"
      cpu: "0.5"
  nodeSelector:
    pool: tools
  tolerations:
    - key: "pool"
      operator: "Equal"
      value: "tools"
      effect: "NoSchedule"