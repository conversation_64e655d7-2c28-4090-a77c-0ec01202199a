apiVersion: rabbitmq.com/v1beta1
kind: RabbitmqCluster
metadata:
  name: rabbitmq
  namespace: rabbitmq
spec:
  replicas: 2
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 1
      memory: 2Gi
  persistence:
    storageClassName: "oci-bv"  # Explicitly specify the default storage class
    storage: "10Gi"
  rabbitmq:
    additionalConfig: |
      cluster_partition_handling = pause_minority
      vm_memory_high_watermark.relative = 0.6
  service:
    type: ClusterIP
  override:
    statefulSet:
      spec:
        template:
          spec:
            containers: []
            nodeSelector:
              pool: tools
            tolerations:
              - key: "pool"
                operator: "Equal"
                value: "tools"
                effect: "NoSchedule"
            affinity:
              podAntiAffinity:
                preferredDuringSchedulingIgnoredDuringExecution:
                - weight: 100
                  podAffinityTerm:
                    labelSelector:
                      matchExpressions:
                      - key: app.kubernetes.io/name
                        operator: In
                        values:
                        - rabbitmq
                    topologyKey: kubernetes.io/hostname
