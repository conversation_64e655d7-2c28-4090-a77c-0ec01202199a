apiVersion: batch/v1
kind: Job
metadata:
  name: db-seed
  namespace: {{ .Values.namespace }}
spec:
  activeDeadlineSeconds: 10000
  ttlSecondsAfterFinished: 100
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
    spec:
      restartPolicy: Never
      containers:
        - image: {{ .Values.image }}
          command:
            - /bin/sh
            - -c
            - node dist/scripts seed:dev
          name: db-seed
          envFrom:
          - configMapRef:
              name: app-config-envs
      imagePullSecrets:
        - name: ocirsecret
