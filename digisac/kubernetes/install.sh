#!/usr/bin/env bash

set -ex

CLUSTER_NAME=${1:"digisac-cluster-1"}

which kubectl >/dev/null 2>&1 || { echo "Error: kubectl not found. Please install it first."; exit 1; }
which istioctl >/dev/null 2>&1 || { echo "Error: istioctl not found. Please install it first."; exit 1; }
which helm >/dev/null 2>&1 || { echo "Error: helm not found. Please install it first."; exit 1; }

# install istio
kubectl create namespace istio-system
istioctl install -f ./tools/istio/istio.yaml -y

# install reloader
helm repo add stakater https://stakater.github.io/stakater-charts
helm repo update
kubectl create namespace reloader
helm upgrade --install reloader stakater/reloader \
  -n reloader \
  -f ./tools/reloader/values.yaml

# install oracle-cluster-autoscaler
helm upgrade --install oracle-cluster-autoscaler ./tools/oracle-cluster-autoscaler \
  -f "./tools/oracle-cluster-autoscaler/${CLUSTER_NAME}-values.yaml"

# install dragonfly (redis)
kubectl apply -k ./tools/dragonfly/operator
kubectl create namespace dragonfly
kubectl apply -f ./tools/dragonfly/dragonfly.yaml

# install rabbitmq
kubectl apply -k ./tools/rabbitmq/operator
kubectl create namespace rabbitmq
kubectl apply -f ./tools/rabbitmq/cluster.yaml

# install pulsar
kubectl create namespace pulsar
helm repo add apache https://pulsar.apache.org/charts
helm repo update
helm upgrade --install pulsar apache/pulsar \
  -n pulsar \
  -f ./tools/pulsar/values.yaml

# install pulsar dekaf
helm upgrade --install dekaf ./tools/dekaf \
  -n pulsar \
  -f "./tools/dekaf/${CLUSTER_NAME}-values.yaml"

# install pgbouncer
kubectl create namespace pgbouncer
helm upgrade --install pgbouncer ./tools/pgbouncer \
  -n pgbouncer \
  -f "./tools/pgbouncer/${CLUSTER_NAME}-values.yaml"

# install ocir-credentials
helm upgrade --install ocir-credentials ./tools/ocir-credentials \
  -f "./tools/ocir-credentials/values.yaml"

# patch coredns
kubectl patch deployment coredns -n kube-system --type=strategic --patch '
spec:
  template:
    spec:
      nodeSelector:
        pool: tools
      tolerations:
      - key: "pool"
        operator: "Equal"
        value: "tools"
        effect: "NoSchedule"
      - key: "CriticalAddonsOnly"
        operator: "Exists"
      - key: "node-role.kubernetes.io/control-plane"
        operator: "Exists"
        effect: "NoSchedule"
      - key: "node-role.kubernetes.io/master"
        operator: "Exists"
        effect: "NoSchedule"
'

# patch kube-dns-autoscaler
kubectl patch deployment kube-dns-autoscaler -n kube-system --type=strategic --patch '
spec:
  template:
    spec:
      nodeSelector:
        pool: tools
      tolerations:
      - key: "pool"
        operator: "Equal"
        value: "tools"
        effect: "NoSchedule"
      - key: "CriticalAddonsOnly"
        operator: "Exists"
      - key: "node-role.kubernetes.io/control-plane"
        operator: "Exists"
        effect: "NoSchedule"
      - key: "node-role.kubernetes.io/master"
        operator: "Exists"
        effect: "NoSchedule"
'
# patch kubernetes-dashboard
kubectl patch deployment kubernetes-dashboard -n kube-system --type=strategic --patch '
spec:
  template:
    spec:
      nodeSelector:
        pool: tools
      tolerations:
      - key: "pool"
        operator: "Equal"
        value: "tools"
        effect: "NoSchedule"
      - key: "CriticalAddonsOnly"
        operator: "Exists"
      - key: "node-role.kubernetes.io/control-plane"
        operator: "Exists"
        effect: "NoSchedule"
      - key: "node-role.kubernetes.io/master"
        operator: "Exists"
        effect: "NoSchedule"
'

kubectl create namespace app