module "global" {
  source            = "modules/global"
  compartment_name  = var.compartment_name
  vcn_name          = var.vcn_name
  fingerprint       = var.fingerprint
  private_key_path  = var.private_key_path
  tenancy_namespace = var.tenancy_namespace
  tenancy_ocid      = var.tenancy_ocid
  user_ocid         = var.user_ocid
  vcn_cidr          = var.vcn_cidr
  container_images_compartment_ocid          = var.container_images_compartment_ocid
}

module "cluster-1" {
  source                       = "modules/cluster"
  cluster_name                 = "digisac-cluster-1"
  public_subnet_cidr           = "10.190.0.0/22"
  private_subnet_cidr          = "10.190.4.0/22"
  postgres_private_subnet_cidr = "10.190.8.0/22"
  postgres_name                = "digisac-cluster-1-postgres"
  compartment_id               = module.global.compartment_id
  fingerprint                  = var.fingerprint
  private_key_path             = var.private_key_path
  ssh_public_key_path          = var.ssh_public_key_path
  tenancy_ocid                 = var.tenancy_ocid
  user_ocid                    = var.user_ocid
  vcn_id                       = module.global.vcn_id
  vcn_cidr                     = var.vcn_cidr
  vpn_cidr                     = var.vpn_cidr
  kubernetes_version           = var.kubernetes_version
  local_peering_gateway_id     = module.global.local_peering_gateway_id
  region                       = var.region
  availability_domain          = var.availability_domain
  node_image_id                = var.node_image_id
  postgres_image_id            = var.postgres_image_id
  postgres_db                  = var.postgres_db
  postgres_user                = var.postgres_user
  postgres_password            = var.postgres_password
  postgres_vm_ocpus            = var.postgres_vm_ocpus
  postgres_vm_memory_in_gbs    = var.postgres_vm_memory_in_gbs
  postgres_playbook_run    = var.postgres_playbook_run
}
