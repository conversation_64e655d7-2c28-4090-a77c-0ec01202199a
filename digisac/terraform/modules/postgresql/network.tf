# PostgreSQL-specific route table
resource "oci_core_route_table" "postgres_route_table" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.postgres_name}-route-table"

  route_rules {
    description       = "Internet Gateway"
    destination       = "0.0.0.0/0"
    network_entity_id = var.nat_gateway_id
  }

  # Rule for VCN Peering
  route_rules {
    description       = "Peering Digisac-Cluster X Tools"
    destination       = "*********/16"
    destination_type  = "CIDR_BLOCK"
    network_entity_id = var.local_peering_gateway_id
  }
}

# PostgreSQL-specific security list
resource "oci_core_security_list" "postgres_security_list" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.postgres_name}-security-list"

  ingress_security_rules {
    description = "Allow internal communication from whole VCN CIDR block."
    protocol = "6" # TCP
    source      = var.vcn_cidr
    tcp_options {
      min = 5432
      max = 5432
    }
  }

  ingress_security_rules {
    description = "Allow SSH from VPN"
    protocol = "6" # TCP
    source      = var.vpn_cidr
    tcp_options {
      min = 22
      max = 22
    }
  }

  ingress_security_rules {
    description = "Allow Postgres 5432 from VPN"
    protocol = "6" # TCP
    source      = var.vpn_cidr
    tcp_options {
      min = 5432
      max = 5432
    }
  }

  egress_security_rules {
    description = "Allow all outbound traffic"
    protocol    = "all"
    destination = "0.0.0.0/0"
  }
}

# Create a private subnet for PostgreSQL
resource "oci_core_subnet" "private_subnet" {
  cidr_block                 = var.private_subnet_cidr
  compartment_id             = var.compartment_id
  vcn_id                     = var.vcn_id
  prohibit_public_ip_on_vnic = true
  route_table_id             = oci_core_route_table.postgres_route_table.id
  security_list_ids = [oci_core_security_list.postgres_security_list.id]
  display_name               = "${var.postgres_name}-private-subnet"
  dns_label                  = "postgresprivsub"
}