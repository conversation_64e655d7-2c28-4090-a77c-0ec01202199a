# Create the PostgreSQL VM instance
resource "oci_core_instance" "postgresql_vm" {
  availability_domain = var.availability_domain
  compartment_id      = var.compartment_id
  shape               = "VM.Standard.E4.Flex"
  display_name        = var.postgres_name

  # Configure instance OCPU and memory
  shape_config {
    ocpus         = var.vm_ocpus
    memory_in_gbs = var.vm_memory_in_gbs
  }

  # Specify the image for the VM
  source_details {
    source_type = "image"
    source_id   = var.image_id
  }

  # Use private subnet and disable public IP
  create_vnic_details {
    subnet_id        = oci_core_subnet.private_subnet.id
    assign_public_ip = false
  }

  metadata = {
    ssh_authorized_keys = file(var.ssh_public_key_path)
  }
}

resource "null_resource" "ansible_provisioner" {
  triggers = {
    playbook_sha1 = filesha1("${path.module}/playbooks/provision.yml")
    vars_sha1 = sha1(jsonencode({
      postgres_user     = var.postgres_user
      postgres_db      = var.postgres_db
      postgres_password = var.postgres_password
      vcn_cidr        = var.vcn_cidr
      vpn_cidr        = var.vpn_cidr
    }))
  }

  provisioner "local-exec" {
    command = <<-EOT
      ANSIBLE_HOST_KEY_CHECKING=False ansible-playbook \
        -i ${oci_core_instance.postgresql_vm.private_ip}, \
        ${path.module}/playbooks/provision.yml \
        -u opc \
        --private-key ${var.ssh_private_key_path} \
        -e 'postgres_user=${var.postgres_user}' \
        -e 'postgres_db=${var.postgres_db}' \
        -e 'postgres_password=${var.postgres_password}' \
        -e 'vcn_cidr=${var.vcn_cidr}' \
        -e 'vpn_cidr=${var.vpn_cidr}' \
        -v
    EOT
  }

  depends_on = [
    oci_core_instance.postgresql_vm
  ]
}