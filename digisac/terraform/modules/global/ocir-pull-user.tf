# Group for OCR image pull access
resource "oci_identity_group" "ocir_pull_group" {
  name           = "ocir-pull-group"
  description    = "Group for users with permissions to pull images from Oracle Container Registry"
  compartment_id = var.tenancy_ocid
}

# Policy to allow pulling images from OCR
resource "oci_identity_policy" "ocir_pull_policy" {
  name           = "ocir-pull-policy"
  description    = "Policy to allow pulling images from Oracle Container Registry"
  compartment_id = var.tenancy_ocid

  statements = [
    "Allow group ${oci_identity_group.ocir_pull_group.name} to inspect repos in compartment id ${var.container_images_compartment_ocid}",
    "Allow group ${oci_identity_group.ocir_pull_group.name} to read repos in compartment id ${var.container_images_compartment_ocid}"
  ]
}

# User for OCR image pulling
resource "oci_identity_user" "ocir_pull_user" {
  name           = "ocir-pull-user"
  description    = "User for pulling images from Oracle Container Registry"
  compartment_id = var.tenancy_ocid
}

# Add user to the OCR pull group
resource "oci_identity_user_group_membership" "ocir_user_group_membership" {
  group_id = oci_identity_group.ocir_pull_group.id
  user_id  = oci_identity_user.ocir_pull_user.id
}

# Auth token for the user
resource "oci_identity_auth_token" "ocir_auth_token" {
  description = "Auth token for OCIR access"
  user_id     = oci_identity_user.ocir_pull_user.id
}

# Output the auth token (Note: This will only be shown once)
output "auth_token" {
  value     = oci_identity_auth_token.ocir_auth_token.token
  sensitive = true
}

# Create values.yaml file with the credentials
resource "local_file" "ocir_values" {
  filename = "../kubernetes/tools/ocir-credentials/values.yaml"
  content  = yamlencode({
    region            = var.region
    namespace         = "app"
    tenancyNamespace = var.tenancy_namespace
    username         = oci_identity_user.ocir_pull_user.name
    authToken        = oci_identity_auth_token.ocir_auth_token.token
    secretName       = "ocirsecret"
  })

  file_permission = "0644"
}
