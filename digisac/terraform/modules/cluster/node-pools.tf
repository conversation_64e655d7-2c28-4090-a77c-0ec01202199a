# Node Pool 1: digisac-statefull (on-demand)
resource "oci_containerengine_node_pool" "digisac_statefull" {
  cluster_id         = oci_containerengine_cluster.digisac_cluster.id
  compartment_id     = var.compartment_id
  kubernetes_version = var.kubernetes_version
  name               = "${var.cluster_name}-sf"
  node_shape         = "VM.Standard.E4.Flex"

  node_source_details {
    image_id    = var.node_image_id
    source_type = "IMAGE"
  }

  node_shape_config {
    ocpus         = 4
    memory_in_gbs = 16
  }

  node_config_details {
    size = 1
    placement_configs {
      availability_domain = var.availability_domain
      subnet_id           = oci_core_subnet.digisac_private_subnet.id
    }
  }

  initial_node_labels {
    key   = "pool"
    value = "statefull"
  }

  node_metadata = {
    pool = "statefull"
  }

  lifecycle {
    ignore_changes = [
      node_config_details[0].size
    ]
  }
}

# Node Pool 2: digisac-stateless-spot (spot)
resource "oci_containerengine_node_pool" "digisac_stateless_spot" {
  cluster_id         = oci_containerengine_cluster.digisac_cluster.id
  compartment_id     = var.compartment_id
  kubernetes_version = var.kubernetes_version
  name               = "${var.cluster_name}-sl-spot"
  node_shape         = "VM.Standard.E4.Flex"

  node_source_details {
    image_id    = var.node_image_id
    source_type = "IMAGE"
  }

  node_shape_config {
    ocpus         = 4
    memory_in_gbs = 16
  }

  node_config_details {
    size = 1
    placement_configs {
      availability_domain = var.availability_domain
      subnet_id           = oci_core_subnet.digisac_private_subnet.id

      # Configuração para spot/preemptible instances
      preemptible_node_config {
        preemption_action {
          type                    = "TERMINATE"
          is_preserve_boot_volume = false
        }
      }
    }
  }

  initial_node_labels {
    key   = "pool"
    value = "stateless-spot"
  }

  node_metadata = {
    pool = "stateless-spot"
  }

  # Configuração para instâncias spot
  node_eviction_node_pool_settings {
    eviction_grace_duration              = "PT0S"
    is_force_delete_after_grace_duration = true
  }

  lifecycle {
    ignore_changes = [
      node_config_details[0].size
    ]
  }
}

# Node Pool 3: digisac-stateless-fallback (on-demand)
resource "oci_containerengine_node_pool" "digisac_stateless_fallback" {
  cluster_id         = oci_containerengine_cluster.digisac_cluster.id
  compartment_id     = var.compartment_id
  kubernetes_version = var.kubernetes_version
  name               = "${var.cluster_name}-sl-fallback"
  node_shape         = "VM.Standard.E4.Flex"

  node_source_details {
    image_id    = var.node_image_id
    source_type = "IMAGE"
  }

  node_shape_config {
    ocpus         = 4
    memory_in_gbs = 16
  }

  node_config_details {
    size = 1
    placement_configs {
      availability_domain = var.availability_domain
      subnet_id           = oci_core_subnet.digisac_private_subnet.id
    }
  }

  initial_node_labels {
    key   = "pool"
    value = "stateless-fallback"
  }

  node_metadata = {
    pool = "stateless-fallback"
  }

  lifecycle {
    ignore_changes = [
      node_config_details[0].size
    ]
  }
}

# Node Pool 4: digisac-tools (on-demand com mais memória)
resource "oci_containerengine_node_pool" "digisac_tools" {
  cluster_id         = oci_containerengine_cluster.digisac_cluster.id
  compartment_id     = var.compartment_id
  kubernetes_version = var.kubernetes_version
  name               = "${var.cluster_name}-tools"
  node_shape         = "VM.Standard.E4.Flex"

  node_source_details {
    image_id    = var.node_image_id
    source_type = "IMAGE"
  }

  node_shape_config {
    ocpus         = 4
    memory_in_gbs = 16
  }

  node_config_details {
    size = 1
    placement_configs {
      availability_domain = var.availability_domain
      subnet_id           = oci_core_subnet.digisac_private_subnet.id
    }
  }

  initial_node_labels {
    key   = "pool"
    value = "tools"
  }

  node_metadata = {
    pool = "tools"
  }

  lifecycle {
    ignore_changes = [
      node_config_details[0].size
    ]
  }
}