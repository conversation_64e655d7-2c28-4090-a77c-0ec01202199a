module "postgresql" {
  source                   = "../postgresql"
  private_subnet_cidr      = var.postgres_private_subnet_cidr
  compartment_id           = var.compartment_id
  vcn_id                   = var.vcn_id
  nat_gateway_id           = oci_core_nat_gateway.digisac_nat.id
  availability_domain      = var.availability_domain
  image_id                 = var.postgres_image_id
  vm_ocpus                 = var.postgres_vm_ocpus
  vm_memory_in_gbs         = var.postgres_vm_memory_in_gbs
  postgres_name            = var.postgres_name
  postgres_user            = var.postgres_user
  postgres_password        = var.postgres_password
  postgres_db              = var.postgres_db
  ssh_private_key_path     = var.private_key_path
  vcn_cidr                 = var.vcn_cidr
  vpn_cidr                 = var.vpn_cidr
  local_peering_gateway_id = var.local_peering_gateway_id
  ssh_public_key_path      = var.ssh_public_key_path
}