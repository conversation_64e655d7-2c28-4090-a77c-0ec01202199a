# Internet Gateway
resource "oci_core_internet_gateway" "digisac_ig" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.cluster_name}-internet-gateway"
  enabled        = true
}

# Route Table for public subnet
resource "oci_core_route_table" "digisac_public_rt" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.cluster_name}-public-route-table"

  route_rules {
    description       = "Internet Gateway"
    destination       = "0.0.0.0/0"
    destination_type  = "CIDR_BLOCK"
    network_entity_id = oci_core_internet_gateway.digisac_ig.id
  }
}

# Security List for public subnet
resource "oci_core_security_list" "digisac_public_sl" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.cluster_name}-public-security-list"

  # Ingress rules

  # public HTTP
  ingress_security_rules {
    description = "Allow public ingress for HTTP"
    protocol = "6" // TCP
    source      = "0.0.0.0/0"
    stateless   = false
    tcp_options {
      min = 80
      max = 80
    }
  }

  # Health check port - restricted to Oracle's health check CIDRs
  ingress_security_rules {
    description = "Istio health check from Oracle's GWLB Endpoints"
    protocol = "6" // TCP
    source = "**********/16" # oracle health check
    stateless   = false
    tcp_options {
      min = 15021
      max = 15021
    }
  }
  ingress_security_rules {
    description = "Istio health check from Oracle's GWLB Endpoints"
    protocol = "6" // TCP
    source = "***********/16" # oracle health check
    stateless   = false
    tcp_options {
      min = 15021
      max = 15021
    }
  }
  ingress_security_rules {
    description = "Istio health check from Oracle's GWLB Endpoints"
    protocol = "6" // TCP
    source = "**********/16" # oracle health check
    stateless   = false
    tcp_options {
      min = 15021
      max = 15021
    }
  }

  # PostgreSQL Exporter - restrict to your monitoring system's CIDR
  ingress_security_rules {
    description = "PostgreSQL Exporter"
    protocol = "6" // TCP
    source      = var.vcn_cidr
    stateless   = false
    tcp_options {
      min = 9187
      max = 9187
    }
  }

  # Egress rules
  egress_security_rules {
    description = "Allow pods on one worker node to communicate with pods on other worker nodes"
    protocol    = "all"
    destination = "0.0.0.0/0"
    stateless   = false
  }
}

# Public Subnet
resource "oci_core_subnet" "digisac_public_subnet" {
  cidr_block     = var.public_subnet_cidr
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.cluster_name}-public-subnet"
  dns_label      = "public"
  route_table_id = oci_core_route_table.digisac_public_rt.id
  security_list_ids = [oci_core_security_list.digisac_public_sl.id]
}

# NAT Gateway for private subnet
resource "oci_core_nat_gateway" "digisac_nat" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.cluster_name}-nat-gateway"
}

# Route Table for private subnet
resource "oci_core_route_table" "digisac_private_rt" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.cluster_name}-private-route-table"

  route_rules {
    description       = "Traffic to the internet"
    destination       = "0.0.0.0/0"
    destination_type  = "CIDR_BLOCK"
    network_entity_id = oci_core_nat_gateway.digisac_nat.id
  }

  # Rule for VCN Peering
  route_rules {
    description       = "Peering Digisac-Cluster X Tools"
    destination       = "*********/16"
    destination_type  = "CIDR_BLOCK"
    network_entity_id = var.local_peering_gateway_id
  }
}

# Security List for private subnet
resource "oci_core_security_list" "digisac_private_sl" {
  compartment_id = var.compartment_id
  vcn_id         = var.vcn_id
  display_name   = "${var.cluster_name}-private-security-list"

  # Ingress rules
  ingress_security_rules {
    description = "Allow pods on one worker node to communicate with pods on other worker nodes"
    protocol    = "all"
    source      = var.vcn_cidr
    stateless   = false
  }

  ingress_security_rules {
    description = "VPN"
    protocol = "6" // TCP
    source      = var.vpn_cidr
    source_type = "CIDR_BLOCK"
    stateless   = false
  }

  # Egress rules
  egress_security_rules {
    description = "Allow all outbound traffic"
    protocol    = "all"
    destination = "0.0.0.0/0"
    stateless   = false
  }
}

# Private Subnet
resource "oci_core_subnet" "digisac_private_subnet" {
  cidr_block                 = var.private_subnet_cidr
  compartment_id             = var.compartment_id
  vcn_id                     = var.vcn_id
  display_name               = "${var.cluster_name}-private-subnet"
  dns_label                  = "private"
  prohibit_public_ip_on_vnic = true
  route_table_id             = oci_core_route_table.digisac_private_rt.id
  security_list_ids = [oci_core_security_list.digisac_private_sl.id]
}