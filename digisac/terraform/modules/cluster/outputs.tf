output "cluster_id" {
  description = "OCID do cluster Kubernetes"
  value       = oci_containerengine_cluster.digisac_cluster.id
}

output "cluster_name" {
  description = "Nome do cluster Kubernetes"
  value       = oci_containerengine_cluster.digisac_cluster.name
}

output "kubernetes_version" {
  description = "Versão do Kubernetes no cluster"
  value       = oci_containerengine_cluster.digisac_cluster.kubernetes_version
}

output "node_pools" {
  description = "Lista de node pools criados"
  value = {
    statefull          = oci_containerengine_node_pool.digisac_statefull.name
    stateless_spot     = oci_containerengine_node_pool.digisac_stateless_spot.name
    stateless_fallback = oci_containerengine_node_pool.digisac_stateless_fallback.name
    tools              = oci_containerengine_node_pool.digisac_tools.name
  }
}

output "cluster_endpoint" {
  description = "Endpoint privado do cluster Kubernetes"
  value       = oci_containerengine_cluster.digisac_cluster.endpoints[0].private_endpoint
}

output "kubeconfig_command" {
  description = "Comando para obter o kubeconfig"
  value       = "oci ce cluster create-kubeconfig --cluster-id ${oci_containerengine_cluster.digisac_cluster.id} --file $HOME/.kube/config --region ${var.region} --token-version 2.0.0"
}