# values.yaml
replicaCount: 1

image:
  repository: "sa-vinhedo-1.ocir.io/axvaplbwrlcl/wall:serverpod-v1.1.0-mr-6.2"
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 50061

resources:
  requests:
    memory: "256Mi"
    cpu: "100m"
  limits:
    memory: "256Mi"
    cpu: "200m"

env: 
  INSTANCE_ID: "serverpod-local-1"
  INSTANCE_TYPE: "default"
  # (por exemplo "serverpod.wall-cluster.svc.cluster.local:50061") ou 
  # simplesmente "serverpod:50061" se estiver no mesmo namespace.
  PUBLIC_ADDRESS: "serverpod:50061"

  GRPC_ADDRESS: ":50061"
  API_GRPC_ADDRESS: "api:50060"
  LOG_TYPE: "text"
  
  # Você comentou que o service do browserless é "browserless" na mesma namespace
  # escutando na porta 3000. N<PERSON>e caso, basta "ws://browserless:3000".
  CHROME_ADDRESS: "ws://browserless:3000"

  WA_VERSION_TOKEN: "**************************"
  S3_REGION: "us-east-1"
  S3_BUCKET_NAME: "mandeumzap-storage-test"
  S3_ACCESS_KEY_ID: "********************"
  S3_SECRET_ACCESS_KEY: "lwkXoFLwGjciJSQWgunlqfZmiq0VaNHnOep6Gt1p"

# Se quiser definir explicitamente um namespace via Helm, 
# pode pôr algo assim. (Ou criar o namespace fora)
namespace: "wall-cluster"
