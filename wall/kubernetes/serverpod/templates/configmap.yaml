apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "serverpod.fullname" . }}-env
  namespace: {{ .Values.namespace | default .Release.Namespace }}
data:
  INSTANCE_ID: {{ .Values.env.INSTANCE_ID | quote }}
  INSTANCE_TYPE: {{ .Values.env.INSTANCE_TYPE | quote }}
  PUBLIC_ADDRESS: {{ .Values.env.PUBLIC_ADDRESS | quote }}
  GRPC_ADDRESS: {{ .Values.env.GRPC_ADDRESS | quote }}
  API_GRPC_ADDRESS: {{ .Values.env.API_GRPC_ADDRESS | quote }}
  LOG_TYPE: {{ .Values.env.LOG_TYPE | quote }}
  CHROME_ADDRESS: {{ .Values.env.CHROME_ADDRESS | quote }}
  WA_VERSION_TOKEN: {{ .Values.env.WA_VERSION_TOKEN | quote }}
  S3_REGION: {{ .Values.env.S3_REGION | quote }}
  S3_BUCKET_NAME: {{ .Values.env.S3_BUCKET_NAME | quote }}
  S3_ACCESS_KEY_ID: {{ .Values.env.S3_ACCESS_KEY_ID | quote }}
  S3_SECRET_ACCESS_KEY: {{ .Values.env.S3_SECRET_ACCESS_KEY | quote }}
