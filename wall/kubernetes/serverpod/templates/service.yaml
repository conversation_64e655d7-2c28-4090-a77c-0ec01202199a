apiVersion: v1
kind: Service
metadata:
  name: {{ include "serverpod.fullname" . }}
  namespace: {{ .Values.namespace | default .Release.Namespace }}
  labels:
    app.kubernetes.io/name: {{ include "serverpod.name" . }}
    helm.sh/chart: {{ include "serverpod.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - name: grpc
      port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.port }}
      protocol: TCP
  selector:
    app.kubernetes.io/name: {{ include "serverpod.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
