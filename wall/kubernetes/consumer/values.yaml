replicaCount: 1

image:
  repository: sa-vinhedo-1.ocir.io/axvaplbwrlcl/wall:consumer-v1.1.0-mr-6.2
  pullPolicy: IfNotPresent

resources:
  requests:
    memory: "256Mi"
  limits:
    memory: "256Mi"

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 80

env:
  SEND_MESSAGE_QUEUE_CONSUMERS: "100"
  WEBHOOKS_CONSUMERS: "100"
  JOBS_GRPC_ADDRESS: "api:50062"
  PULSAR_URL: "pulsar://pulsar-proxy.pulsar.svc.cluster.local:6650"

serviceAccount:
  create: false

namespace: wall-cluster
