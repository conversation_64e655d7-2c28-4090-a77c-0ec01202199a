apiVersion: apps/v1
kind: Deployment
metadata:
  name: consumer
  namespace: {{ .Values.namespace }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: consumer
  template:
    metadata:
      labels:
        app: consumer
    spec:
      containers:
        - name: consumer
          image: "{{ .Values.image.repository }}"
          envFrom:
            - configMapRef:
                name: consumer-env
          resources:
            requests:
              memory: {{ .Values.resources.requests.memory }}
            limits:
              memory: {{ .Values.resources.limits.memory }}
