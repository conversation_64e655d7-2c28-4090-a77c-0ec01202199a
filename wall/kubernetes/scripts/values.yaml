image:
  repository: sa-vinhedo-1.ocir.io/axvaplbwrlcl/wall:scripts-v1.1.0-mr-6.2

config:
  apiAddress: ":5060"
  serverPodApiGrpcAddress: ":50060"
  jobsGrpcAddress: ":50062"
  publicGrpcAddress: ":50063"
  jwtSecretKey: "\\6RUtw)cX>P8o4*+J+Hp7i8J#'w.A]k~+l?6(1n\\:,pqPQA_+["
  dbHost: "postgres-wall.wall-cluster.svc.cluster.local"
  dbPort: "5432"
  dbUsername: "postgres"
  dbPassword: "postgres"
  dbName: "wall"
  dbMaxConns: "500"
  dbLog: "true"