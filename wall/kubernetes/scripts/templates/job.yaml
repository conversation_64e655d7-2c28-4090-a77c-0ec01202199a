apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "scripts.fullname" . }}
  namespace: {{ .Release.Namespace }}
spec:
  template:
    spec:
      containers:
        - name: scripts
          image: {{ .Values.image.repository }}:{{ .Values.image.tag }}
          envFrom:
            - configMapRef:
                name: {{ include "scripts.fullname" . }}-env
          resources:
            requests:
              memory: "256Mi"
          command: ["sleep", "infinity"]
      restartPolicy: Never