apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "scripts.fullname" . }}-env
  namespace: {{ .Release.Namespace }}
data:
  API_ADDRESS: {{ .Values.config.apiAddress | quote }}
  SERVER_POD_API_GRPC_ADDRESS: {{ .Values.config.serverPodApiGrpcAddress | quote }}
  JOBS_GRPC_ADDRESS: {{ .Values.config.jobsGrpcAddress | quote }}
  PUBLIC_GRPC_ADDRESS: {{ .Values.config.publicGrpcAddress | quote }}
  JWT_SECRET_KEY: {{ .Values.config.jwtSecretKey | quote }}
  DB_HOST: {{ .Values.config.dbHost | quote }}
  DB_PORT: {{ .Values.config.dbPort | quote }}
  DB_USERNAME: {{ .Values.config.dbUsername | quote }}
  DB_PASSWORD: {{ .Values.config.dbPassword | quote }}
  DB_NAME: {{ .Values.config.dbName | quote }}
  DB_MAX_CONNS: {{ .Values.config.dbMaxConns | quote }}
  DB_LOG: {{ .Values.config.dbLog | quote }}