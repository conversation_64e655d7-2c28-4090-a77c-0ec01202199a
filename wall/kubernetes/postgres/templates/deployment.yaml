apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "postgres.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "postgres.name" . }}
spec:
  replicas: {{ .Values.config.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "postgres.name" . }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "postgres.name" . }}
    spec:
      containers:
        - name: postgres
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          ports:
            - containerPort: 5432
          env:
            - name: POSTGRES_USER
              value: "{{ .Values.config.postgresUser }}"
            - name: POSTGRES_PASSWORD
              value: "{{ .Values.config.postgresPassword }}"
            - name: POSTGRES_DB
              value: "{{ .Values.config.postgresDatabase }}"
            - name: PGDATA
              value: "{{ .Values.config.postgresData }}"
          volumeMounts:
            - name: postgres-data
              mountPath: /var/lib/postgresql/data
      volumes:
        - name: postgres-data
          persistentVolumeClaim:
            claimName: {{ include "postgres.fullname" . }}-pvc
