apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "browserless.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "browserless.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "browserless.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "browserless.selectorLabels" . | nindent 8 }}
    spec:
      securityContext:
        runAsUser: 0
        runAsGroup: 0
      hostAliases:
        - ip: "127.0.0.1"
          hostnames:
            - "crashlogs.whatsapp.net"
      containers:
        - name: browserless
          image: {{ .Values.image.repository }}:{{ .Values.image.tag }}
          command: ["/bin/sh", "-c", "find /usr/src/app/wp-sessions -type f -name 'SingletonLock' -delete && ./scripts/start.sh"]
          env:
            - name: CONCURRENT
              value: "100"
            - name: TIMEOUT
              value: "0"
            - name: TOKEN
              value: "6R0W53R135510"
            - name: ERROR_ALERT_URL
              value: ""
          ports:
            - containerPort: 3000
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: sessions
              mountPath: /usr/src/app/wp-sessions
            - name: dshm
              mountPath: /dev/shm
      volumes:
        - name: sessions
          emptyDir: {} # ou persistentVolumeClaim, se quiser persistir
        - name: dshm
          emptyDir:
            medium: Memory
            sizeLimit: 2Gi
