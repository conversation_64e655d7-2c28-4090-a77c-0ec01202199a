{{- if .Values.pvc.enabled }}
{{- if .Values.persistence.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "browserless.fullname" . }}-pvc
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "browserless.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.persistence.accessMode | default "ReadWriteOnce" }}
  resources:
    requests:
      storage: {{ .Values.persistence.size | default "20Gi" }}
  storageClassName: {{ .Values.persistence.storageClass | quote }}
{{- end }}
{{- end }}