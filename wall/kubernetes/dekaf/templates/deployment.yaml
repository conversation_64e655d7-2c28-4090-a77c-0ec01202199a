apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "dekaf.fullname" . }}
  namespace: {{ .Values.namespace }}
  labels:
    app: dekaf
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: dekaf
  template:
    metadata:
      labels:
        app: dekaf
    spec:
      containers:
        - name: dekaf
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: 8090
          env:
            - name: PULSAR_BROKER_URL
              value: "{{ .Values.pulsar.brokerUrl }}"
          resources:
            requests:
              memory: {{ .Values.resources.requests.memory }}
            limits:
              memory: {{ .Values.resources.limits.memory }}
