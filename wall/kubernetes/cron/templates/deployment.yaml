apiVersion: apps/v1
kind: Deployment
metadata:
  name: cron
  namespace: {{ .Values.namespace }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: cron
  template:
    metadata:
      labels:
        app: cron
    spec:
      containers:
        - name: cron
          image: {{ .Values.image.cron }}
          envFrom:
            - configMapRef:
                name: cron-env
          resources:
            requests:
              memory: {{ .Values.resources.requests.memory }}
            limits:
              memory: {{ .Values.resources.limits.memory }}
