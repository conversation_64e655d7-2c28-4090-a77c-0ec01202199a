replicaCount: 1

namespace: wall-cluster

image:
  repository: sa-vinhedo-1.ocir.io/axvaplbwrlcl/wall:api-v1.1.0-mr-6.2
  pullPolicy: IfNotPresent

service:
  type: LoadBalancer
  ports:
    http: 5060
    grpc1: 50060
    grpc2: 50062
    grpc3: 50063

resources:
  requests:
    memory: "256Mi"
  limits:
    memory: "256Mi"

env:
  API_ADDRESS: ":5060"
  SERVER_POD_API_GRPC_ADDRESS: ":50060"
  JOBS_GRPC_ADDRESS: ":50062"
  PUBLIC_GRPC_ADDRESS: ":50063"
  JWT_SECRET_KEY: "\\6RUtw)cX>P8o4*+J+Hp7i8J#'w.A]k~+l?6(1n\\:,pqPQA_+["
  DB_HOST: "postgres-wall.wall-cluster.svc.cluster.local"
  DB_PORT: "5432"
  DB_USERNAME: "postgres"
  DB_PASSWORD: "postgres"
  DB_NAME: "wall"
  DB_MAX_CONNS: "500"
  DB_LOG: "true"
  PULSAR_URL: "pulsar://pulsar-proxy.pulsar.svc.cluster.local:6650"


autoscaling:
  enabled: true
  # Número mínimo de réplicas
  minReplicas: 1
  # Número máximo de réplicas
  maxReplicas: 5

  # Se quiser usar métricas de CPU:
  targetCPUUtilizationPercentage: 80

  # Se quiser usar métricas de memória:
  # (opcional - você pode remover se não quiser escalar por memória)
  targetMemoryUtilizationPercentage: 80
