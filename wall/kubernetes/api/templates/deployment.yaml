apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
  namespace: {{ .Values.namespace }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: api
  template:
    metadata:
      labels:
        app: api
    spec:
      containers:
        - name: api
          image: "{{ .Values.image.repository }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: {{ .Values.service.ports.http }}
            - containerPort: {{ .Values.service.ports.grpc1 }}
            - containerPort: {{ .Values.service.ports.grpc2 }}
            - containerPort: {{ .Values.service.ports.grpc3 }}
          envFrom:
            - configMapRef:
                name: api-env
          resources:
            requests:
              memory: {{ .Values.resources.requests.memory | quote }}
            limits:
              memory: {{ .Values.resources.limits.memory | quote }}
resources:
  requests:
    cpu: "100m"
    memory: "256Mi"
  limits:
    cpu: "500m"
    memory: "512Mi"
