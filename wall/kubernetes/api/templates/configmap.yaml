apiVersion: v1
kind: ConfigMap
metadata:
  name: api-env
  namespace: {{ .Values.namespace }}
data:
  API_ADDRESS: {{ .Values.env.API_ADDRESS | quote }}
  SERVER_POD_API_GRPC_ADDRESS: {{ .Values.env.SERVER_POD_API_GRPC_ADDRESS | quote }}
  JOBS_GRPC_ADDRESS: {{ .Values.env.JOBS_GRPC_ADDRESS | quote }}
  PUBLIC_GRPC_ADDRESS: {{ .Values.env.PUBLIC_GRPC_ADDRESS | quote }}
  JWT_SECRET_KEY: {{ .Values.env.JWT_SECRET_KEY | quote }}
  DB_HOST: {{ .Values.env.DB_HOST | quote }}
  DB_PORT: {{ .Values.env.DB_PORT | quote }}
  DB_USERNAME: {{ .Values.env.DB_USERNAME | quote }}
  DB_PASSWORD: {{ .Values.env.DB_PASSWORD | quote }}
  DB_NAME: {{ .Values.env.DB_NAME | quote }}
  DB_MAX_CONNS: {{ .Values.env.DB_MAX_CONNS | quote }}
  DB_LOG: {{ .Values.env.DB_LOG | quote }}
  PULSAR_URL: {{ .Values.env.PULSAR_URL | quote }}
