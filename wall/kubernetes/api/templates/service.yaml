apiVersion: v1
kind: Service
metadata:
  name: api
  namespace: {{ .Values.namespace }}
spec:
  type: {{ .Values.service.type }}
  selector:
    app: api
  ports:
    - name: api-http
      port: {{ .Values.service.ports.http }}
      targetPort: {{ .Values.service.ports.http }}
    - name: api-grpc-1
      port: {{ .Values.service.ports.grpc1 }}
      targetPort: {{ .Values.service.ports.grpc1 }}
    - name: api-grpc-2
      port: {{ .Values.service.ports.grpc2 }}
      targetPort: {{ .Values.service.ports.grpc2 }}
    - name: api-grpc-3
      port: {{ .Values.service.ports.grpc3 }}
      targetPort: {{ .Values.service.ports.grpc3 }}
