# 1. Stateless Spot Node Pool
resource "tencentcloud_kubernetes_node_pool" "stateless_spot" {
  name                     = "stateless-spot"
  cluster_id               = tencentcloud_kubernetes_cluster.k8s_cluster.id
  max_size                 = var.max_node_count
  min_size                 = var.min_node_count
  vpc_id                   = tencentcloud_vpc.k8s_vpc.id
  subnet_ids               = [tencentcloud_subnet.private_subnet.id]
  enable_auto_scale        = true
  multi_zone_subnet_policy = "EQUALITY"

  auto_scaling_config {
    instance_type              = var.node_instance_type
    system_disk_type           = var.node_disk_type
    system_disk_size           = var.node_disk_size
    orderly_security_group_ids = [tencentcloud_security_group.cluster_sg.id]
    instance_charge_type       = "SPOTPAID"
    spot_instance_type         = "one-time"
    spot_max_price             = "1000"
    key_ids                    = [tencentcloud_key_pair.cluster_key.id]

    data_disk {
      disk_type = var.node_disk_type
      disk_size = var.node_disk_size
    }

    public_ip_assigned         = false
  }

  labels = {
    "node-pool-type" = "stateless-spot"
  }
}

# 2. Stateless Fallback Node Pool (On Demand)
resource "tencentcloud_kubernetes_node_pool" "stateless_fallback" {
  name                     = "stateless-fallback"
  cluster_id               = tencentcloud_kubernetes_cluster.k8s_cluster.id
  max_size                 = var.max_node_count
  min_size                 = 0
  vpc_id                   = tencentcloud_vpc.k8s_vpc.id
  subnet_ids               = [tencentcloud_subnet.private_subnet.id]
  enable_auto_scale        = true
  multi_zone_subnet_policy = "EQUALITY"

  auto_scaling_config {
    instance_type              = var.node_instance_type
    system_disk_type           = var.node_disk_type
    system_disk_size           = var.node_disk_size
    orderly_security_group_ids = [tencentcloud_security_group.cluster_sg.id]
    instance_charge_type       = "POSTPAID_BY_HOUR"
    key_ids                    = [tencentcloud_key_pair.cluster_key.id]

    data_disk {
      disk_type = var.node_disk_type
      disk_size = var.node_disk_size
    }

    public_ip_assigned         = false
  }

  labels = {
    "node-pool-type" = "stateless-fallback"
  }
}

# 3. Stateful Node Pool (On Demand)
resource "tencentcloud_kubernetes_node_pool" "stateful" {
  name                     = "stateful"
  cluster_id               = tencentcloud_kubernetes_cluster.k8s_cluster.id
  max_size                 = var.max_node_count
  min_size                 = var.min_node_count
  vpc_id                   = tencentcloud_vpc.k8s_vpc.id
  subnet_ids               = [tencentcloud_subnet.private_subnet.id]
  enable_auto_scale        = true
  multi_zone_subnet_policy = "EQUALITY"

  auto_scaling_config {
    instance_type              = var.node_instance_type
    system_disk_type           = var.node_disk_type
    system_disk_size           = var.node_disk_size
    orderly_security_group_ids = [tencentcloud_security_group.cluster_sg.id]
    instance_charge_type       = "POSTPAID_BY_HOUR"
    key_ids                    = [tencentcloud_key_pair.cluster_key.id]

    data_disk {
      disk_type = var.node_disk_type
      disk_size = var.node_disk_size * 2  # Larger disk for stateful workloads
    }

    public_ip_assigned         = false
  }

  labels = {
    "node-pool-type" = "stateful"
  }
}

# 4. WhatsApp Spot Node Pool
resource "tencentcloud_kubernetes_node_pool" "whatsapp_spot" {
  name                     = "whatsapp-spot"
  cluster_id               = tencentcloud_kubernetes_cluster.k8s_cluster.id
  max_size                 = var.max_node_count
  min_size                 = var.min_node_count
  vpc_id                   = tencentcloud_vpc.k8s_vpc.id
  subnet_ids               = [tencentcloud_subnet.public_whatsapp.id]
  enable_auto_scale        = true
  multi_zone_subnet_policy = "EQUALITY"

  auto_scaling_config {
    instance_type              = var.node_instance_type
    system_disk_type           = var.node_disk_type
    system_disk_size           = var.node_disk_size
    orderly_security_group_ids = [tencentcloud_security_group.cluster_sg.id]
    instance_charge_type       = "SPOTPAID"
    spot_instance_type         = "one-time"
    spot_max_price             = "1000"
    key_ids                    = [tencentcloud_key_pair.cluster_key.id]

    data_disk {
      disk_type = var.node_disk_type
      disk_size = var.node_disk_size
    }

    internet_charge_type       = "TRAFFIC_POSTPAID_BY_HOUR"
    internet_max_bandwidth_out = 100
    public_ip_assigned         = true
  }

  labels = {
    "node-pool-type" = "whatsapp-spot"
  }
}

# 5. WhatsApp Fallback Node Pool (On Demand)
resource "tencentcloud_kubernetes_node_pool" "whatsapp_fallback" {
  name                     = "whatsapp-fallback"
  cluster_id               = tencentcloud_kubernetes_cluster.k8s_cluster.id
  max_size                 = var.max_node_count
  min_size                 = 0
  vpc_id                   = tencentcloud_vpc.k8s_vpc.id
  subnet_ids               = [tencentcloud_subnet.public_whatsapp.id]
  enable_auto_scale        = true
  multi_zone_subnet_policy = "EQUALITY"

  auto_scaling_config {
    instance_type              = var.node_instance_type
    system_disk_type           = var.node_disk_type
    system_disk_size           = var.node_disk_size
    orderly_security_group_ids = [tencentcloud_security_group.cluster_sg.id]
    instance_charge_type       = "POSTPAID_BY_HOUR"
    key_ids                    = [tencentcloud_key_pair.cluster_key.id]

    data_disk {
      disk_type = var.node_disk_type
      disk_size = var.node_disk_size
    }

    internet_charge_type       = "TRAFFIC_POSTPAID_BY_HOUR"
    internet_max_bandwidth_out = 100
    public_ip_assigned         = true
  }

  labels = {
    "node-pool-type" = "whatsapp-fallback"
  }
}

# 6. Tools Node Pool (On Demand)
resource "tencentcloud_kubernetes_node_pool" "tools" {
  name                     = "tools"
  cluster_id               = tencentcloud_kubernetes_cluster.k8s_cluster.id
  max_size                 = var.max_node_count
  min_size                 = var.min_node_count
  vpc_id                   = tencentcloud_vpc.k8s_vpc.id
  subnet_ids               = [tencentcloud_subnet.private_subnet.id]
  enable_auto_scale        = true
  multi_zone_subnet_policy = "EQUALITY"

  auto_scaling_config {
    instance_type              = var.node_instance_type
    system_disk_type           = var.node_disk_type
    system_disk_size           = var.node_disk_size
    orderly_security_group_ids = [tencentcloud_security_group.cluster_sg.id]
    instance_charge_type       = "POSTPAID_BY_HOUR"
    key_ids                    = [tencentcloud_key_pair.cluster_key.id]

    data_disk {
      disk_type = var.node_disk_type
      disk_size = var.node_disk_size
    }

    public_ip_assigned         = false
  }

  labels = {
    "node-pool-type" = "tools"
  }
}