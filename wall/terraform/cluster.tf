# SSH key for all nodes
resource "tencentcloud_key_pair" "cluster_key" {
  key_name   = "${var.cluster_name}_key"
  public_key = file(pathexpand(var.ssh_public_key_path))
}

# Kubernetes Cluster
resource "tencentcloud_kubernetes_cluster" "k8s_cluster" {
  vpc_id                          = tencentcloud_vpc.k8s_vpc.id
  cluster_name                    = var.cluster_name
  cluster_version                 = var.cluster_version
  cluster_cidr                    = var.cluster_cidr
  cluster_max_pod_num             = 256
  cluster_max_service_num         = 512
  cluster_deploy_type             = "MANAGED_CLUSTER"
  cluster_internet_security_group = tencentcloud_security_group.cluster_sg.id
  container_runtime          = "containerd"
}

# Initialize Kubernetes provider for further configurations
provider "kubernetes" {
  host                   = tencentcloud_kubernetes_cluster.k8s_cluster.cluster_external_endpoint
  client_certificate     = base64decode(tencentcloud_kubernetes_cluster.k8s_cluster.kube_config[0].client_certificate)
  client_key             = base64decode(tencentcloud_kubernetes_cluster.k8s_cluster.kube_config[0].client_key)
  cluster_ca_certificate = base64decode(tencentcloud_kubernetes_cluster.k8s_cluster.kube_config[0].cluster_ca_certificate)
}