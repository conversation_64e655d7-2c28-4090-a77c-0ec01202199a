{"version": 4, "terraform_version": "1.11.0", "serial": 98, "lineage": "85ad3577-5872-2247-1cd0-6813365474a4", "outputs": {"cluster_ca_certificate": {"value": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIICyDCCAbCgAwIBAgIBADANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDEwprdWJl\ncm5ldGVzMB4XDTI1MDMxMTIxMTM1NVoXDTM1MDMwOTIxMTM1NVowFTETMBEGA1UE\nAxMKa3ViZXJuZXRlczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJkC\nvOz611bFlik32Ryiygq+6ALt3C+Ndmk9Q1WSkwqGyDo+w7p/SAeuhc5nwvudyWdS\njqRTerzyV8zheBr+FFoC/0Zhp7E3vZNXUvEpVU4ZwCQ5IZqvlX20gbATIp+SV250\nguq49zxCR7IMp33fvhaDOpP/lSyN+npiapo2yHJ3SXpi3LUtP9E6wZ31erl6Um/4\nVgroY6zwsNYGxMJOkfAO3IGdOXxq1veuMjCd10kSHykhHPE5YqyDEhe/+h4sPO8g\nPP4DyuQR6K0/uAC14ZGS0W/mVLccyHjcg+9IQ8P8LdwS8/9VM5w9vZ64lBbd0PbI\nIZs2N+iiYD3n8jWyI0kCAwEAAaMjMCEwDgYDVR0PAQH/BAQDAgKUMA8GA1UdEwEB\n/wQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEBAEc/6u8swS1GFpxG0vYj29cVIbdg\nIMYnztYGBdUGSz2c1ouGlzojtSBrWFdSqwRoOPkLIyrWXAflQDJvPIl63K0/j5mC\ngX3qOKMLa8vMwDv6ddBE5Zpt0UzRbtLIHIfINwde7hdeX2ZojfbG/WUnGl/8n8+2\nFST7HCZp/ozPP1cj12+nRWoIO5TJupVazn1mGjHbns8yVzDzKaXxOPgo6cP1Dl+j\n56yZV2cQ2BU1/DeY8JqmE301UNgZ8QxYMwy+5Yk+9Sb72C8ZBe2k5P7iYwKCYz2p\nonOcUXhF/VmDfZFi/Y0Mr6JTLAwrOWshR9NIm+7geSzs7cpnr4/wPfVjW8s=\n-----END CERTIFICATE-----\n", "type": "string", "sensitive": true}, "cluster_endpoint": {"value": "", "type": "string"}, "cluster_id": {"value": "cls-cz5r946f", "type": "string"}, "cluster_kubeconfig": {"value": "", "type": "string", "sensitive": true}, "nat_eip_address": {"value": "*************", "type": "string"}, "nat_gateway_id": {"value": "nat-56k5x7ks", "type": "string"}, "private_subnet_id": {"value": "subnet-gn6825q2", "type": "string"}, "public_subnet_id": {"value": "subnet-9yyu2pkm", "type": "string"}, "public_whatsapp_subnet_id": {"value": "subnet-pq4ash0y", "type": "string"}, "stateful_node_pool_id": {"value": "cls-cz5r946f#np-p1hczyav", "type": "string"}, "stateless_fallback_node_pool_id": {"value": "cls-cz5r946f#np-4mhcfnhh", "type": "string"}, "stateless_spot_node_pool_id": {"value": "cls-cz5r946f#np-m2pdf4qt", "type": "string"}, "tools_node_pool_id": {"value": "cls-cz5r946f#np-n9kvhi27", "type": "string"}, "vpc_id": {"value": "vpc-kwnoutv1", "type": "string"}, "whatsapp_fallback_node_pool_id": {"value": "cls-cz5r946f#np-ljad0929", "type": "string"}, "whatsapp_spot_node_pool_id": {"value": "cls-cz5r946f#np-diy8ynop", "type": "string"}}, "resources": [{"mode": "managed", "type": "tencentcloud_eip", "name": "nat_eip", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"anti_ddos_package_id": null, "anycast_zone": null, "applicable_for_clb": null, "auto_renew_flag": null, "bandwidth_package_id": null, "cdc_id": null, "egress": "center_egress1", "id": "eip-f8id7mxh", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 1, "internet_service_provider": null, "name": "wall-nat-eip", "prepaid_period": null, "public_ip": "*************", "status": "BIND", "tags": {}, "type": "EIP"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "tencentcloud_key_pair", "name": "cluster_key", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"id": "skey-furakmzf", "key_name": "wall_key", "project_id": 0, "public_key": "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC0cj8YdQ8sII7rCjONRK8nO8CP3yOK46bLYedqJlCUb8lyiIFnNd0D/xgGllMbFe2nEn9HnuyYrYPIJelNLA2b7Jw4jr7RLizKuVIfie6HDY6nIfaoUvxqNqE+3f1wuZXFi36jh7+9Yxm7fViSJZTssy0vld6VE57ynrCiRfjk0MCcLDN35cD6P4P3Tpg8RxdKrXDXyXgOtTPLfEGXgW8C7H1ojDx+LfKLItnevm38LBggfaHaMqs3+ByFr+HTlTBz5y9LSFn2yHOLissd+7CkttGaYJ7bAck97XBBMDGtwf8LMlHBw0oBhJAhlItGO9yc4n4kJFX73pyFNK/MqEM04fZN+9z2FIVWzlPlMxNP1q+DQTC3rJQ27VTTxi582stw4m3+j3s1bRcQiUMpA6au4IALtdn1AfBcnirgrzBCm4+3BWMGQWJhNr5yRyJev0U2wLgI5Q3twidPaytZIk0OsWYITsx3zD+uNr9uNhabp9MkonNsvm1rQX/9cnpr1uzEjEo3ZYLT/QZFEzkrobA2K40OZ2VKMT26sBQyaDIbwg6dUW269VnjIGL2blgbcDoIECicB1ZUhVTlo6EZMGVx8tlxup6qc5gRXlrXnHbBWXaCsvNbJA5S1MinQIYDdBARjf4aIqZBwCFshCmAu5goqtuauzdytFQTKap3hGJ/vw==", "tags": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_cluster", "name": "k8s_cluster", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"acquire_cluster_admin_role": null, "auth_options": [], "auto_upgrade_cluster_level": null, "base_pod_num": null, "cdc_id": "", "certification_authority": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIICyDCCAbCgAwIBAgIBADANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDEwprdWJl\ncm5ldGVzMB4XDTI1MDMxMTIxMTM1NVoXDTM1MDMwOTIxMTM1NVowFTETMBEGA1UE\nAxMKa3ViZXJuZXRlczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJkC\nvOz611bFlik32Ryiygq+6ALt3C+Ndmk9Q1WSkwqGyDo+w7p/SAeuhc5nwvudyWdS\njqRTerzyV8zheBr+FFoC/0Zhp7E3vZNXUvEpVU4ZwCQ5IZqvlX20gbATIp+SV250\nguq49zxCR7IMp33fvhaDOpP/lSyN+npiapo2yHJ3SXpi3LUtP9E6wZ31erl6Um/4\nVgroY6zwsNYGxMJOkfAO3IGdOXxq1veuMjCd10kSHykhHPE5YqyDEhe/+h4sPO8g\nPP4DyuQR6K0/uAC14ZGS0W/mVLccyHjcg+9IQ8P8LdwS8/9VM5w9vZ64lBbd0PbI\nIZs2N+iiYD3n8jWyI0kCAwEAAaMjMCEwDgYDVR0PAQH/BAQDAgKUMA8GA1UdEwEB\n/wQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEBAEc/6u8swS1GFpxG0vYj29cVIbdg\nIMYnztYGBdUGSz2c1ouGlzojtSBrWFdSqwRoOPkLIyrWXAflQDJvPIl63K0/j5mC\ngX3qOKMLa8vMwDv6ddBE5Zpt0UzRbtLIHIfINwde7hdeX2ZojfbG/WUnGl/8n8+2\nFST7HCZp/ozPP1cj12+nRWoIO5TJupVazn1mGjHbns8yVzDzKaXxOPgo6cP1Dl+j\n56yZV2cQ2BU1/DeY8JqmE301UNgZ8QxYMwy+5Yk+9Sb72C8ZBe2k5P7iYwKCYz2p\nonOcUXhF/VmDfZFi/Y0Mr6JTLAwrOWshR9NIm+7geSzs7cpnr4/wPfVjW8s=\n-----END CERTIFICATE-----\n", "claim_expired_seconds": 300, "cluster_as_enabled": null, "cluster_audit": [], "cluster_cidr": "**********/16", "cluster_deploy_type": "MANAGED_CLUSTER", "cluster_desc": "", "cluster_external_endpoint": "", "cluster_extra_args": [], "cluster_internet": false, "cluster_internet_domain": null, "cluster_internet_security_group": "sg-9gseiv0b", "cluster_intranet": false, "cluster_intranet_domain": null, "cluster_intranet_subnet_id": null, "cluster_ipvs": true, "cluster_level": "L5", "cluster_max_pod_num": 256, "cluster_max_service_num": 512, "cluster_name": "wall", "cluster_node_num": 0, "cluster_os": "tlinux2.4x86_64", "cluster_os_type": "GENERAL", "cluster_subnet_id": null, "cluster_version": "1.30.0", "container_runtime": "containerd", "deletion_protection": false, "docker_graph_path": null, "domain": "cls-cz5r946f.ccs.tencent-cloud.com", "enable_customized_pod_cidr": false, "eni_subnet_ids": null, "event_persistence": [], "exist_instance": [], "extension_addon": [], "extra_args": null, "globe_desired_pod_num": null, "id": "cls-cz5r946f", "ignore_cluster_cidr_conflict": false, "ignore_service_cidr_conflict": false, "instance_delete_mode": null, "is_non_static_ip_mode": false, "kube_config": "", "kube_config_intranet": "", "kube_proxy_mode": null, "labels": null, "log_agent": [], "managed_cluster_internet_security_policies": null, "master_config": [], "mount_target": null, "network_type": "GR", "node_name_type": "lan-ip", "node_pool_global_config": [{"expander": "random", "ignore_daemon_sets_utilization": false, "is_scale_in_enabled": false, "max_concurrent_scale_in": 10, "scale_in_delay": 10, "scale_in_unneeded_time": 10, "scale_in_utilization_threshold": 50, "skip_nodes_with_local_storage": true, "skip_nodes_with_system_pods": true}], "password": "PzpEE8xbSpCBLvMnap9g1GQRjUsGyxoK", "pgw_endpoint": "", "pre_start_user_script": null, "project_id": 0, "resource_delete_options": [], "runtime_version": null, "security_policy": [], "service_cidr": null, "tags": null, "unschedulable": null, "upgrade_instances_follow_cluster": false, "user_name": "admin", "vpc_cni_type": "", "vpc_id": "vpc-kwnoutv1", "worker_config": [], "worker_instances_list": []}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_security_group.cluster_sg", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_node_pool", "name": "stateful", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": [], "auto_scaling_config": [{"backup_instance_types": null, "bandwidth_package_id": "", "cam_role_name": "", "data_disk": [{"delete_with_instance": false, "disk_size": 200, "disk_type": "CLOUD_PREMIUM", "encrypt": false, "snapshot_id": "", "throughput_performance": 0}], "enhanced_monitor_service": true, "enhanced_security_service": true, "host_name": "", "host_name_style": "", "instance_charge_type": "POSTPAID_BY_HOUR", "instance_charge_type_prepaid_period": 0, "instance_charge_type_prepaid_renew_flag": "", "instance_name": "", "instance_name_style": "", "instance_type": "S5.2XLARGE16", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 0, "key_ids": ["skey-furakmzf"], "orderly_security_group_ids": ["sg-9gseiv0b"], "password": "", "public_ip_assigned": false, "security_group_ids": ["sg-9gseiv0b"], "spot_instance_type": "", "spot_max_price": "", "system_disk_size": 100, "system_disk_type": "CLOUD_PREMIUM"}], "auto_scaling_group_id": "asg-iqfnt1v1", "auto_update_instance_tags": false, "autoscaling_added_total": 1, "cluster_id": "cls-cz5r946f", "default_cooldown": 300, "delete_keep_instance": true, "deletion_protection": false, "desired_capacity": 1, "enable_auto_scale": true, "id": "cls-cz5r946f#np-p1hczyav", "labels": {"node-pool-type": "stateful"}, "launch_config_id": "asc-c1at22ah", "manually_added_total": 0, "max_size": 5, "min_size": 1, "multi_zone_subnet_policy": "EQUALITY", "name": "stateful", "node_config": [], "node_count": 1, "node_os": "tlinux2.4x86_64", "node_os_type": "GENERAL", "retry_policy": "IMMEDIATE_RETRY", "scale_tolerance": null, "scaling_group_name": "tke-np-p1hczyav", "scaling_group_project_id": 0, "scaling_mode": null, "status": "normal", "subnet_ids": ["subnet-gn6825q2"], "tags": {}, "taints": [], "termination_policies": ["OLDEST_INSTANCE"], "timeouts": null, "unschedulable": 0, "vpc_id": "vpc-kwnoutv1", "wait_node_ready": null, "zones": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "auto_scaling_config"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "password"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJ1cGRhdGUiOjE4MDAwMDAwMDAwMDB9fQ==", "dependencies": ["tencentcloud_key_pair.cluster_key", "tencentcloud_kubernetes_cluster.k8s_cluster", "tencentcloud_security_group.cluster_sg", "tencentcloud_subnet.private_subnet", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_node_pool", "name": "stateless_fallback", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": [], "auto_scaling_config": [{"backup_instance_types": null, "bandwidth_package_id": "", "cam_role_name": "", "data_disk": [{"delete_with_instance": false, "disk_size": 100, "disk_type": "CLOUD_PREMIUM", "encrypt": false, "snapshot_id": "", "throughput_performance": 0}], "enhanced_monitor_service": true, "enhanced_security_service": true, "host_name": "", "host_name_style": "", "instance_charge_type": "POSTPAID_BY_HOUR", "instance_charge_type_prepaid_period": 0, "instance_charge_type_prepaid_renew_flag": "", "instance_name": "", "instance_name_style": "", "instance_type": "S5.2XLARGE16", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 0, "key_ids": ["skey-furakmzf"], "orderly_security_group_ids": ["sg-9gseiv0b"], "password": "", "public_ip_assigned": false, "security_group_ids": ["sg-9gseiv0b"], "spot_instance_type": "", "spot_max_price": "", "system_disk_size": 100, "system_disk_type": "CLOUD_PREMIUM"}], "auto_scaling_group_id": "asg-e47dosj1", "auto_update_instance_tags": false, "autoscaling_added_total": 0, "cluster_id": "cls-cz5r946f", "default_cooldown": 300, "delete_keep_instance": true, "deletion_protection": false, "desired_capacity": 0, "enable_auto_scale": true, "id": "cls-cz5r946f#np-4mhcfnhh", "labels": {"node-pool-type": "stateless-fallback"}, "launch_config_id": "asc-1uk4uz2z", "manually_added_total": 0, "max_size": 5, "min_size": 0, "multi_zone_subnet_policy": "EQUALITY", "name": "stateless-fallback", "node_config": [], "node_count": 0, "node_os": "tlinux2.4x86_64", "node_os_type": "GENERAL", "retry_policy": "IMMEDIATE_RETRY", "scale_tolerance": null, "scaling_group_name": "tke-np-4mhcfnhh", "scaling_group_project_id": 0, "scaling_mode": null, "status": "normal", "subnet_ids": ["subnet-gn6825q2"], "tags": {}, "taints": [], "termination_policies": ["OLDEST_INSTANCE"], "timeouts": null, "unschedulable": 0, "vpc_id": "vpc-kwnoutv1", "wait_node_ready": null, "zones": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "auto_scaling_config"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "password"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJ1cGRhdGUiOjE4MDAwMDAwMDAwMDB9fQ==", "dependencies": ["tencentcloud_key_pair.cluster_key", "tencentcloud_kubernetes_cluster.k8s_cluster", "tencentcloud_security_group.cluster_sg", "tencentcloud_subnet.private_subnet", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_node_pool", "name": "stateless_spot", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": [], "auto_scaling_config": [{"backup_instance_types": null, "bandwidth_package_id": "", "cam_role_name": "", "data_disk": [{"delete_with_instance": false, "disk_size": 100, "disk_type": "CLOUD_PREMIUM", "encrypt": false, "snapshot_id": "", "throughput_performance": 0}], "enhanced_monitor_service": true, "enhanced_security_service": true, "host_name": "", "host_name_style": "", "instance_charge_type": "SPOTPAID", "instance_charge_type_prepaid_period": 0, "instance_charge_type_prepaid_renew_flag": "", "instance_name": "", "instance_name_style": "", "instance_type": "S5.2XLARGE16", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 0, "key_ids": ["skey-furakmzf"], "orderly_security_group_ids": ["sg-9gseiv0b"], "password": "", "public_ip_assigned": false, "security_group_ids": ["sg-9gseiv0b"], "spot_instance_type": "one-time", "spot_max_price": "1000", "system_disk_size": 100, "system_disk_type": "CLOUD_PREMIUM"}], "auto_scaling_group_id": "asg-n4cxm1qh", "auto_update_instance_tags": false, "autoscaling_added_total": 1, "cluster_id": "cls-cz5r946f", "default_cooldown": 300, "delete_keep_instance": true, "deletion_protection": false, "desired_capacity": 1, "enable_auto_scale": true, "id": "cls-cz5r946f#np-m2pdf4qt", "labels": {"node-pool-type": "stateless-spot"}, "launch_config_id": "asc-clh8stn7", "manually_added_total": 0, "max_size": 5, "min_size": 1, "multi_zone_subnet_policy": "EQUALITY", "name": "stateless-spot", "node_config": [], "node_count": 1, "node_os": "tlinux2.4x86_64", "node_os_type": "GENERAL", "retry_policy": "IMMEDIATE_RETRY", "scale_tolerance": null, "scaling_group_name": "tke-np-m2pdf4qt", "scaling_group_project_id": 0, "scaling_mode": null, "status": "normal", "subnet_ids": ["subnet-gn6825q2"], "tags": {}, "taints": [], "termination_policies": ["OLDEST_INSTANCE"], "timeouts": null, "unschedulable": 0, "vpc_id": "vpc-kwnoutv1", "wait_node_ready": null, "zones": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "auto_scaling_config"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "password"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJ1cGRhdGUiOjE4MDAwMDAwMDAwMDB9fQ==", "dependencies": ["tencentcloud_key_pair.cluster_key", "tencentcloud_kubernetes_cluster.k8s_cluster", "tencentcloud_security_group.cluster_sg", "tencentcloud_subnet.private_subnet", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_node_pool", "name": "tools", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": [], "auto_scaling_config": [{"backup_instance_types": null, "bandwidth_package_id": "", "cam_role_name": "", "data_disk": [{"delete_with_instance": false, "disk_size": 100, "disk_type": "CLOUD_PREMIUM", "encrypt": false, "snapshot_id": "", "throughput_performance": 0}], "enhanced_monitor_service": true, "enhanced_security_service": true, "host_name": "", "host_name_style": "", "instance_charge_type": "POSTPAID_BY_HOUR", "instance_charge_type_prepaid_period": 0, "instance_charge_type_prepaid_renew_flag": "", "instance_name": "", "instance_name_style": "", "instance_type": "S5.2XLARGE16", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 0, "key_ids": ["skey-furakmzf"], "orderly_security_group_ids": ["sg-9gseiv0b"], "password": "", "public_ip_assigned": false, "security_group_ids": ["sg-9gseiv0b"], "spot_instance_type": "", "spot_max_price": "", "system_disk_size": 100, "system_disk_type": "CLOUD_PREMIUM"}], "auto_scaling_group_id": "asg-03lum5i9", "auto_update_instance_tags": false, "autoscaling_added_total": 1, "cluster_id": "cls-cz5r946f", "default_cooldown": 300, "delete_keep_instance": true, "deletion_protection": false, "desired_capacity": 1, "enable_auto_scale": true, "id": "cls-cz5r946f#np-n9kvhi27", "labels": {"node-pool-type": "tools"}, "launch_config_id": "asc-fbwlqjbx", "manually_added_total": 0, "max_size": 5, "min_size": 1, "multi_zone_subnet_policy": "EQUALITY", "name": "tools", "node_config": [], "node_count": 1, "node_os": "tlinux2.4x86_64", "node_os_type": "GENERAL", "retry_policy": "IMMEDIATE_RETRY", "scale_tolerance": null, "scaling_group_name": "tke-np-n9kvhi27", "scaling_group_project_id": 0, "scaling_mode": null, "status": "normal", "subnet_ids": ["subnet-gn6825q2"], "tags": {}, "taints": [], "termination_policies": ["OLDEST_INSTANCE"], "timeouts": null, "unschedulable": 0, "vpc_id": "vpc-kwnoutv1", "wait_node_ready": null, "zones": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "auto_scaling_config"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "password"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJ1cGRhdGUiOjE4MDAwMDAwMDAwMDB9fQ==", "dependencies": ["tencentcloud_key_pair.cluster_key", "tencentcloud_kubernetes_cluster.k8s_cluster", "tencentcloud_security_group.cluster_sg", "tencentcloud_subnet.private_subnet", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_node_pool", "name": "whatsapp_fallback", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": [], "auto_scaling_config": [{"backup_instance_types": null, "bandwidth_package_id": "", "cam_role_name": "", "data_disk": [{"delete_with_instance": false, "disk_size": 100, "disk_type": "CLOUD_PREMIUM", "encrypt": false, "snapshot_id": "", "throughput_performance": 0}], "enhanced_monitor_service": true, "enhanced_security_service": true, "host_name": "", "host_name_style": "", "instance_charge_type": "POSTPAID_BY_HOUR", "instance_charge_type_prepaid_period": 0, "instance_charge_type_prepaid_renew_flag": "", "instance_name": "", "instance_name_style": "", "instance_type": "S5.2XLARGE16", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 100, "key_ids": ["skey-furakmzf"], "orderly_security_group_ids": ["sg-9gseiv0b"], "password": "", "public_ip_assigned": true, "security_group_ids": ["sg-9gseiv0b"], "spot_instance_type": "", "spot_max_price": "", "system_disk_size": 100, "system_disk_type": "CLOUD_PREMIUM"}], "auto_scaling_group_id": "asg-mzy7alrj", "auto_update_instance_tags": false, "autoscaling_added_total": 0, "cluster_id": "cls-cz5r946f", "default_cooldown": 300, "delete_keep_instance": true, "deletion_protection": false, "desired_capacity": 0, "enable_auto_scale": true, "id": "cls-cz5r946f#np-ljad0929", "labels": {"node-pool-type": "whatsapp-fallback"}, "launch_config_id": "asc-9irqhvxz", "manually_added_total": 0, "max_size": 5, "min_size": 0, "multi_zone_subnet_policy": "EQUALITY", "name": "whatsapp-fallback", "node_config": [], "node_count": 0, "node_os": "tlinux2.4x86_64", "node_os_type": "GENERAL", "retry_policy": "IMMEDIATE_RETRY", "scale_tolerance": null, "scaling_group_name": "tke-np-ljad0929", "scaling_group_project_id": 0, "scaling_mode": null, "status": "normal", "subnet_ids": ["subnet-pq4ash0y"], "tags": {}, "taints": [], "termination_policies": ["OLDEST_INSTANCE"], "timeouts": null, "unschedulable": 0, "vpc_id": "vpc-kwnoutv1", "wait_node_ready": null, "zones": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "auto_scaling_config"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "password"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJ1cGRhdGUiOjE4MDAwMDAwMDAwMDB9fQ==", "dependencies": ["tencentcloud_key_pair.cluster_key", "tencentcloud_kubernetes_cluster.k8s_cluster", "tencentcloud_security_group.cluster_sg", "tencentcloud_subnet.public_whatsapp", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_kubernetes_node_pool", "name": "whatsapp_spot", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": [], "auto_scaling_config": [{"backup_instance_types": null, "bandwidth_package_id": "", "cam_role_name": "", "data_disk": [{"delete_with_instance": false, "disk_size": 100, "disk_type": "CLOUD_PREMIUM", "encrypt": false, "snapshot_id": "", "throughput_performance": 0}], "enhanced_monitor_service": true, "enhanced_security_service": true, "host_name": "", "host_name_style": "", "instance_charge_type": "SPOTPAID", "instance_charge_type_prepaid_period": 0, "instance_charge_type_prepaid_renew_flag": "", "instance_name": "", "instance_name_style": "", "instance_type": "S5.2XLARGE16", "internet_charge_type": "TRAFFIC_POSTPAID_BY_HOUR", "internet_max_bandwidth_out": 100, "key_ids": ["skey-furakmzf"], "orderly_security_group_ids": ["sg-9gseiv0b"], "password": "", "public_ip_assigned": true, "security_group_ids": ["sg-9gseiv0b"], "spot_instance_type": "one-time", "spot_max_price": "1000", "system_disk_size": 100, "system_disk_type": "CLOUD_PREMIUM"}], "auto_scaling_group_id": "asg-9mewmvl7", "auto_update_instance_tags": false, "autoscaling_added_total": 1, "cluster_id": "cls-cz5r946f", "default_cooldown": 300, "delete_keep_instance": true, "deletion_protection": false, "desired_capacity": 1, "enable_auto_scale": true, "id": "cls-cz5r946f#np-diy8ynop", "labels": {"node-pool-type": "whatsapp-spot"}, "launch_config_id": "asc-kcou71w3", "manually_added_total": 0, "max_size": 5, "min_size": 1, "multi_zone_subnet_policy": "EQUALITY", "name": "whatsapp-spot", "node_config": [], "node_count": 1, "node_os": "tlinux2.4x86_64", "node_os_type": "GENERAL", "retry_policy": "IMMEDIATE_RETRY", "scale_tolerance": null, "scaling_group_name": "tke-np-diy8ynop", "scaling_group_project_id": 0, "scaling_mode": null, "status": "normal", "subnet_ids": ["subnet-pq4ash0y"], "tags": {}, "taints": [], "termination_policies": ["OLDEST_INSTANCE"], "timeouts": null, "unschedulable": 0, "vpc_id": "vpc-kwnoutv1", "wait_node_ready": null, "zones": null}, "sensitive_attributes": [[{"type": "get_attr", "value": "auto_scaling_config"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "password"}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJ1cGRhdGUiOjE4MDAwMDAwMDAwMDB9fQ==", "dependencies": ["tencentcloud_key_pair.cluster_key", "tencentcloud_kubernetes_cluster.k8s_cluster", "tencentcloud_security_group.cluster_sg", "tencentcloud_subnet.public_whatsapp", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_nat_gateway", "name": "nat_gateway", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"assigned_eip_set": ["*************"], "bandwidth": 100, "created_time": "2025-03-12 02:37:47", "id": "nat-56k5x7ks", "max_concurrent": 1000000, "name": "wall-nat", "nat_product_version": 1, "stock_public_ip_addresses_bandwidth_out": 1, "subnet_id": "", "tags": {}, "vpc_id": "vpc-kwnoutv1", "zone": "sa-saopaulo-1"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_eip.nat_eip", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_route_table", "name": "private_route_table", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"create_time": "2025-03-12 02:37:33", "id": "rtb-aklm58v4", "is_default": false, "name": "private-route-table", "route_entry_ids": ["160091.rtb-aklm58v4"], "subnet_ids": ["subnet-gn6825q2"], "tags": {}, "vpc_id": "vpc-kwnoutv1"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_route_table_association", "name": "private_association", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"id": "subnet-gn6825q2", "route_table_id": "rtb-aklm58v4", "subnet_id": "subnet-gn6825q2"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_route_table.private_route_table", "tencentcloud_subnet.private_subnet", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_route_table_entry", "name": "internet_route", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"description": "", "destination_cidr_block": "0.0.0.0/0", "disabled": false, "id": "160091.rtb-aklm58v4", "next_hub": "nat-56k5x7ks", "next_type": "NAT", "route_item_id": "rti-pwtpnz89", "route_table_id": "rtb-aklm58v4"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_eip.nat_eip", "tencentcloud_nat_gateway.nat_gateway", "tencentcloud_route_table.private_route_table", "tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_security_group", "name": "cluster_sg", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"description": "Security group for Kubernetes cluster", "id": "sg-9gseiv0b", "name": "wall-sg", "project_id": 0, "tags": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "tencentcloud_security_group_rule", "name": "cluster_internal_egress", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"address_template": [], "cidr_ip": "0.0.0.0/0", "description": "", "id": "eyJzZ19pZCI6InNnLTlnc2VpdjBiIiwicG9saWN5X3R5cGUiOiJlZ3Jlc3MiLCJjaWRyX2lwIjoiMC4wLjAuMC8wIiwicHJvdG9jb2wiOiJBTEwiLCJwb3J0X3JhbmdlIjoiQUxMIiwiYWN0aW9uIjoiQUNDRVBUIiwic291cmNlX3NnX2lkIjoiIiwiZGVzY3JpcHRpb24iOiIifQ==", "ip_protocol": "ALL", "policy": "ACCEPT", "policy_index": null, "port_range": "ALL", "protocol_template": [], "security_group_id": "sg-9gseiv0b", "source_sgid": null, "type": "egress"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_security_group.cluster_sg"]}]}, {"mode": "managed", "type": "tencentcloud_security_group_rule", "name": "cluster_internal_ingress_http", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"address_template": [], "cidr_ip": "0.0.0.0/0", "description": "", "id": "eyJzZ19pZCI6InNnLTlnc2VpdjBiIiwicG9saWN5X3R5cGUiOiJpbmdyZXNzIiwiY2lkcl9pcCI6IjAuMC4wLjAvMCIsInByb3RvY29sIjoiVENQIiwicG9ydF9yYW5nZSI6IjgwLDQ0MyIsImFjdGlvbiI6IkFDQ0VQVCIsInNvdXJjZV9zZ19pZCI6IiIsImRlc2NyaXB0aW9uIjoiIn0=", "ip_protocol": "TCP", "policy": "ACCEPT", "policy_index": null, "port_range": "80,443", "protocol_template": [], "security_group_id": "sg-9gseiv0b", "source_sgid": null, "type": "ingress"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_security_group.cluster_sg"]}]}, {"mode": "managed", "type": "tencentcloud_security_group_rule", "name": "cluster_internal_ingress_k8s_api", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"address_template": [], "cidr_ip": "0.0.0.0/0", "description": "", "id": "eyJzZ19pZCI6InNnLTlnc2VpdjBiIiwicG9saWN5X3R5cGUiOiJpbmdyZXNzIiwiY2lkcl9pcCI6IjAuMC4wLjAvMCIsInByb3RvY29sIjoiVENQIiwicG9ydF9yYW5nZSI6IjY0NDMiLCJhY3Rpb24iOiJBQ0NFUFQiLCJzb3VyY2Vfc2dfaWQiOiIiLCJkZXNjcmlwdGlvbiI6IiJ9", "ip_protocol": "TCP", "policy": "ACCEPT", "policy_index": null, "port_range": "6443", "protocol_template": [], "security_group_id": "sg-9gseiv0b", "source_sgid": null, "type": "ingress"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_security_group.cluster_sg"]}]}, {"mode": "managed", "type": "tencentcloud_security_group_rule", "name": "cluster_internal_ingress_ssh", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"address_template": [], "cidr_ip": "0.0.0.0/0", "description": "", "id": "eyJzZ19pZCI6InNnLTlnc2VpdjBiIiwicG9saWN5X3R5cGUiOiJpbmdyZXNzIiwiY2lkcl9pcCI6IjAuMC4wLjAvMCIsInByb3RvY29sIjoiVENQIiwicG9ydF9yYW5nZSI6IjIyIiwiYWN0aW9uIjoiQUNDRVBUIiwic291cmNlX3NnX2lkIjoiIiwiZGVzY3JpcHRpb24iOiIifQ==", "ip_protocol": "TCP", "policy": "ACCEPT", "policy_index": null, "port_range": "22", "protocol_template": [], "security_group_id": "sg-9gseiv0b", "source_sgid": null, "type": "ingress"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_security_group.cluster_sg"]}]}, {"mode": "managed", "type": "tencentcloud_security_group_rule", "name": "cluster_internal_ingress_vpc", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"address_template": [], "cidr_ip": "10.0.0.0/16", "description": "", "id": "eyJzZ19pZCI6InNnLTlnc2VpdjBiIiwicG9saWN5X3R5cGUiOiJpbmdyZXNzIiwiY2lkcl9pcCI6IjEwLjAuMC4wLzE2IiwicHJvdG9jb2wiOiJBTEwiLCJwb3J0X3JhbmdlIjoiQUxMIiwiYWN0aW9uIjoiQUNDRVBUIiwic291cmNlX3NnX2lkIjoiIiwiZGVzY3JpcHRpb24iOiIifQ==", "ip_protocol": "ALL", "policy": "ACCEPT", "policy_index": null, "port_range": "ALL", "protocol_template": [], "security_group_id": "sg-9gseiv0b", "source_sgid": null, "type": "ingress"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_security_group.cluster_sg"]}]}, {"mode": "managed", "type": "tencentcloud_subnet", "name": "private_subnet", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"availability_zone": "sa-saopaulo-1", "available_ip_count": 1014, "cdc_id": "", "cidr_block": "********/22", "create_time": "2025-03-12 03:53:59", "id": "subnet-gn6825q2", "is_default": false, "is_multicast": false, "name": "private-subnet", "route_table_id": "rtb-aklm58v4", "tags": {"cluster": "wall", "terraform": "true", "usage": "nodes"}, "vpc_id": "vpc-kwnoutv1"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_subnet", "name": "public_subnet", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"availability_zone": "sa-saopaulo-1", "available_ip_count": 1021, "cdc_id": "", "cidr_block": "10.0.0.0/22", "create_time": "2025-03-12 03:53:57", "id": "subnet-9yyu2pkm", "is_default": false, "is_multicast": false, "name": "public-subnet", "route_table_id": "rtb-kb55sxd0", "tags": {"cluster": "wall", "terraform": "true", "usage": "load-balancers"}, "vpc_id": "vpc-kwnoutv1"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_subnet", "name": "public_whatsapp", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"availability_zone": "sa-saopaulo-1", "available_ip_count": 1019, "cdc_id": "", "cidr_block": "********/22", "create_time": "2025-03-12 03:53:57", "id": "subnet-pq4ash0y", "is_default": false, "is_multicast": false, "name": "public-whatsapp", "route_table_id": "rtb-kb55sxd0", "tags": {"cluster": "wall", "terraform": "true", "usage": "whatsapp"}, "vpc_id": "vpc-kwnoutv1"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["tencentcloud_vpc.k8s_vpc"]}]}, {"mode": "managed", "type": "tencentcloud_vpc", "name": "k8s_vpc", "provider": "provider[\"registry.terraform.io/tencentcloudstack/tencentcloud\"]", "instances": [{"schema_version": 0, "attributes": {"assistant_cidrs": [], "cidr_block": "10.0.0.0/16", "create_time": "2025-03-12 02:37:29", "default_route_table_id": "rtb-kb55sxd0", "dns_servers": ["************", "*******"], "docker_assistant_cidrs": ["**********/16"], "id": "vpc-kwnoutv1", "is_default": false, "is_multicast": false, "name": "wall-vpc", "tags": {"cluster": "wall", "terraform": "true"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}], "check_results": null}