apiVersion: batch/v1
kind: Job
metadata:
    name: db-migrate
    namespace: app
spec:
  activeDeadlineSeconds: 10000
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
    spec:
      containers:
      - command:
        - /bin/sh
        - -c
        - node ./node_modules/.bin/sequelize db:migrate --debug
        envFrom:
        - configMapRef:
            name: app-config-envs
        image: sa-vinhedo-1.ocir.io/axvaplbwrlcl/digisac-isolated/back:3.48.2-mr-2376.79
        imagePullPolicy: "IfNotPresent"
        name: db-migrate
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: pool
                operator: In
                values: 
                - statefull
      imagePullSecrets:
      - name: ocirsecret
      restartPolicy: Never
  ttlSecondsAfterFinished: 100
