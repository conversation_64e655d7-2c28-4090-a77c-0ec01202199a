# Terraform and Kubernetes Infrastructure on OCI

This project provides Infrastructure as Code (IaC) for deploying and managing multiple Kubernetes clusters on Oracle Cloud Infrastructure (OCI). It includes both Terraform configurations for infrastructure provisioning and Kubernetes resources for cluster management.

## Project Structure

The project is organized into two main components:

### Terraform (`/terraform`)

#### Modules

1. **Global Module** (`/modules/global`)
   - Manages shared infrastructure components
   - Central VCN (**********/16)
   - Compartment management
   - Common provider configurations
   - Local peering gateways for inter-cluster communication

2. **Cluster Module** (`/modules/cluster`)
   - Individual Kubernetes cluster (OKE) configuration
   - Dedicated node pools per cluster
   - Cluster-specific network configuration
   - Autoscaling setup
   - PostgreSQL instance provisioning
   - Bastion service

3. **PostgreSQL Module** (`/modules/postgresql`)
   - Dedicated PostgreSQL infrastructure per cluster
   - Database networking
   - Security configurations

#### Root Configuration
- `main.tf`: Orchestrates 14 cluster deployments
- `variables.tf`: Variable definitions
- `*.tvars`: Environment-specific configurations
- State management files

### Network Architecture

#### Global VCN
- Address space: `**********/16`
- Local peering gateways for inter-cluster connectivity
- Shared security rules

#### Per-Cluster Subnets
Each cluster gets dedicated subnet ranges:

1. **Cluster 1**: 
   - Public: `**********/22`
   - Private: `**********/22`
   - Database: `**********/22`

2. **Cluster 2**:
   - Public: `***********/22`
   - Private: `***********/22`
   - Database: `***********/22`

[...continues for all 14 clusters]

14. **Cluster 14**:
    - Public: `************/22`
    - Private: `************/22`
    - Database: `************/22`

### Kubernetes (`/kubernetes`)

#### Tools (`/tools`)
Standard tools deployed to each cluster:
- **Service Mesh**: Istio configuration
- **Monitoring**: Prometheus setup
- **Database**: PGBouncer for connection pooling
- **Message Queue**: RabbitMQ
- **Caching**: Dragonfly
- **Logging**: Elastic Cloud on Kubernetes (ECK)
- **Scaling**: Oracle Cluster Autoscaler
- **Networking**: TCP Proxy implementation

#### Per-Cluster Configuration
- Cluster-specific values files
- Individual tool configurations
- Custom resource limits

## Infrastructure Components

### Cluster Configuration
Each cluster includes:
- Type: `ENHANCED_CLUSTER`
- Private endpoint configuration
- Integrated with OCI Cloud Controller
- Kubernetes Dashboard enabled
- Dedicated subnets
- Individual PostgreSQL instance

### Node Pools (Per Cluster)
1. **Production Workloads**
   - Standard shape: 4 OCPU, 16GB RAM
   - On-demand instances
   - Auto-scaling enabled

2. **Cost-Optimized Workloads**
   - Spot instances
   - Preemptible configuration
   - Fallback to on-demand instances

3. **Tools and System Services**
   - Dedicated node pool
   - Higher memory allocation
   - System priority taints

## Getting Started

### Prerequisites
1. **Tools Installation**
   - OCI CLI
   - Terraform ≥ 1.0.0
   - kubectl
   - Helm 3

2. **Access Configuration**
   - OCI account and credentials
   - API key pair
   - Proper IAM permissions

### Deployment Steps

1. **Initialize Terraform**
   ```bash
   cd terraform
   terraform init
   ```

2. **Configure Variables**
   - Copy `main.tvars.example` to `main.tvars`
   - Configure cluster-specific variables

3. **Deploy Infrastructure**
   ```bash
   terraform plan -var-file=main.tvars
   terraform apply -var-file=main.tvars
   ```

4. **Install Kubernetes Tools**
   For each cluster:
   ```bash
   cd ../kubernetes
   ./install.sh <cluster-name>
   ```

## Maintenance

### Scaling
- Independent scaling for each cluster
- Node pools auto-scale based on resource utilization
- Manual scaling available through Terraform
- Cluster Autoscaler manages pod-driven scaling

### Monitoring
- Centralized monitoring with Prometheus
- Cross-cluster metrics aggregation
- Individual Grafana dashboards per cluster
- Unified logging with ECK

### Backup and Recovery
- Automated state backups
- Per-cluster database backup strategy
- Disaster recovery procedures
- Cross-cluster failover capabilities

## Operations

### Cluster Management
- Individual kubeconfig per cluster
- Cluster-specific bastion access
- Separate PostgreSQL instances
- Independent scaling policies

### Network Management
- Inter-cluster communication via local peering
- Isolated security domains
- Cross-cluster service discovery
- Traffic management between clusters

## Contributing
1. Fork the repository
2. Create a feature branch
3. Submit a pull request

## License
Proprietary - All rights reserved

## Support
Contact the infrastructure team for support and questions.